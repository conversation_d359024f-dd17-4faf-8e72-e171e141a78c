package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.BlockEspManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;

import java.util.List;

public class BlockEspConfigScreen extends Screen {
    private final Screen parent;
    private final BlockEspManager blockEspManager;
    private TextFieldWidget blockNameField;
    private ButtonWidget addButton;
    private ButtonWidget removeButton;
    private ButtonWidget clearButton;
    private int selectedBlockIndex = -1;
    private List<String> trackedBlocks;
    
    public BlockEspConfigScreen(Screen parent) {
        super(Text.literal("Block ESP Configuration"));
        this.parent = parent;
        this.blockEspManager = CustomModClient.getInstance().getBlockEspManager();
    }
    
    @Override
    protected void init() {
        super.init();
        
        trackedBlocks = blockEspManager.getTrackedBlocks();
        
        // Block name input field
        blockNameField = new TextFieldWidget(this.textRenderer, this.width / 2 - 100, 50, 200, 20, Text.literal("Block name"));
        blockNameField.setPlaceholder(Text.literal("Enter block name (e.g., diamond_ore)"));
        this.addSelectableChild(blockNameField);
        
        // Add block button
        addButton = ButtonWidget.builder(Text.literal("Add Block"), button -> {
            String blockName = blockNameField.getText().trim();
            if (!blockName.isEmpty()) {
                blockEspManager.addBlock(blockName);
                trackedBlocks = blockEspManager.getTrackedBlocks();
                blockNameField.setText("");
            }
        }).dimensions(this.width / 2 - 100, 80, 95, 20).build();
        this.addDrawableChild(addButton);
        
        // Remove block button
        removeButton = ButtonWidget.builder(Text.literal("Remove Block"), button -> {
            String blockName = blockNameField.getText().trim();
            if (!blockName.isEmpty()) {
                blockEspManager.removeBlock(blockName);
                trackedBlocks = blockEspManager.getTrackedBlocks();
                blockNameField.setText("");
                selectedBlockIndex = -1;
            }
        }).dimensions(this.width / 2 + 5, 80, 95, 20).build();
        this.addDrawableChild(removeButton);
        
        // Clear all blocks button
        clearButton = ButtonWidget.builder(Text.literal("Clear All"), button -> {
            blockEspManager.clearBlocks();
            trackedBlocks = blockEspManager.getTrackedBlocks();
            selectedBlockIndex = -1;
        }).dimensions(this.width / 2 - 50, 110, 100, 20).build();
        this.addDrawableChild(clearButton);
        
        // Add default blocks button
        ButtonWidget defaultButton = ButtonWidget.builder(Text.literal("Add Defaults"), button -> {
            blockEspManager.addDefaultBlocks();
            trackedBlocks = blockEspManager.getTrackedBlocks();
        }).dimensions(this.width / 2 - 50, 140, 100, 20).build();
        this.addDrawableChild(defaultButton);
        
        // Range slider (simplified as buttons for now)
        float currentRange = blockEspManager.getRange();
        ButtonWidget rangeDownButton = ButtonWidget.builder(Text.literal("-"), button -> {
            float newRange = Math.max(1.0f, blockEspManager.getRange() - 5.0f);
            blockEspManager.setRange(newRange);
        }).dimensions(this.width / 2 - 60, 170, 20, 20).build();
        this.addDrawableChild(rangeDownButton);
        
        ButtonWidget rangeUpButton = ButtonWidget.builder(Text.literal("+"), button -> {
            float newRange = Math.min(64.0f, blockEspManager.getRange() + 5.0f);
            blockEspManager.setRange(newRange);
        }).dimensions(this.width / 2 + 40, 170, 20, 20).build();
        this.addDrawableChild(rangeUpButton);
        
        // Back button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Back"), button -> {
            this.client.setScreen(parent);
        }).dimensions(this.width / 2 - 50, this.height - 30, 100, 20).build());
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        // Title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, 0xFFFFFF);
        
        // Instructions
        context.drawCenteredTextWithShadow(this.textRenderer, "Enter block names in English (e.g., diamond_ore)", this.width / 2, 35, 0xAAAAAA);
        
        // Range display
        String rangeText = String.format("Range: %.0f blocks", blockEspManager.getRange());
        context.drawCenteredTextWithShadow(this.textRenderer, rangeText, this.width / 2, 175, 0xFFFFFF);
        
        super.render(context, mouseX, mouseY, delta);
        
        // Draw tracked blocks list
        context.drawText(this.textRenderer, "Tracked Blocks:", this.width / 2 - 100, 200, 0xFFFFFF, false);
        
        int y = 215;
        for (int i = 0; i < trackedBlocks.size() && i < 10; i++) {
            String blockName = trackedBlocks.get(i);
            int color = blockEspManager.getBlockColor(blockName);
            
            // Draw color indicator
            context.fill(this.width / 2 - 100, y, this.width / 2 - 95, y + 10, color);
            
            // Draw block name
            boolean isSelected = i == selectedBlockIndex;
            int textColor = isSelected ? 0xFFFF00 : 0xFFFFFF;
            context.drawText(this.textRenderer, blockName, this.width / 2 - 90, y + 1, textColor, false);
            
            y += 12;
        }
        
        if (trackedBlocks.size() > 10) {
            context.drawText(this.textRenderer, "... and " + (trackedBlocks.size() - 10) + " more", 
                           this.width / 2 - 100, y, 0xAAAAAA, false);
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle clicking on tracked blocks list
        if (mouseX >= this.width / 2 - 100 && mouseX <= this.width / 2 + 100) {
            int startY = 215;
            int clickedIndex = (int) ((mouseY - startY) / 12);
            
            if (clickedIndex >= 0 && clickedIndex < Math.min(trackedBlocks.size(), 10)) {
                selectedBlockIndex = clickedIndex;
                blockNameField.setText(trackedBlocks.get(clickedIndex));
                return true;
            }
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (blockNameField.isFocused()) {
            if (keyCode == 257) { // Enter key
                String blockName = blockNameField.getText().trim();
                if (!blockName.isEmpty()) {
                    blockEspManager.addBlock(blockName);
                    trackedBlocks = blockEspManager.getTrackedBlocks();
                    blockNameField.setText("");
                }
                return true;
            }
            return blockNameField.keyPressed(keyCode, scanCode, modifiers);
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    @Override
    public boolean charTyped(char chr, int modifiers) {
        if (blockNameField.isFocused()) {
            return blockNameField.charTyped(chr, modifiers);
        }
        return super.charTyped(chr, modifiers);
    }
}
