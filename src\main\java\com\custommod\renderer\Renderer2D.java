package com.custommod.renderer;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.VertexFormat;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.render.*;
import net.minecraft.client.util.math.MatrixStack;
import org.joml.Matrix4f;

public class Renderer2D {
    private static final MinecraftClient mc = MinecraftClient.getInstance();
    
    public static void drawRect(MatrixStack matrices, float x, float y, float width, float height, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        drawRect(matrices, x, y, width, height, red, green, blue, alpha);
    }
    
    public static void drawRect(MatrixStack matrices, float x, float y, float width, float height, 
                               float red, float green, float blue, float alpha) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();
        
        buffer.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        buffer.vertex(matrix, x, y + height, 0).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, x + width, y + height, 0).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, x + width, y, 0).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, x, y, 0).color(red, green, blue, alpha).next();
        
        tessellator.draw();
        RenderSystem.disableBlend();
    }
    
    public static void drawOutline(MatrixStack matrices, float x, float y, float width, float height, 
                                  float lineWidth, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        drawOutline(matrices, x, y, width, height, lineWidth, red, green, blue, alpha);
    }
    
    public static void drawOutline(MatrixStack matrices, float x, float y, float width, float height, 
                                  float lineWidth, float red, float green, float blue, float alpha) {
        // Top
        drawRect(matrices, x, y, width, lineWidth, red, green, blue, alpha);
        // Bottom
        drawRect(matrices, x, y + height - lineWidth, width, lineWidth, red, green, blue, alpha);
        // Left
        drawRect(matrices, x, y, lineWidth, height, red, green, blue, alpha);
        // Right
        drawRect(matrices, x + width - lineWidth, y, lineWidth, height, red, green, blue, alpha);
    }
    
    public static void drawLine(MatrixStack matrices, float x1, float y1, float x2, float y2, 
                               float lineWidth, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        drawLine(matrices, x1, y1, x2, y2, lineWidth, red, green, blue, alpha);
    }
    
    public static void drawLine(MatrixStack matrices, float x1, float y1, float x2, float y2, 
                               float lineWidth, float red, float green, float blue, float alpha) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        RenderSystem.lineWidth(lineWidth);
        
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();
        
        buffer.begin(VertexFormat.DrawMode.DEBUG_LINES, VertexFormats.POSITION_COLOR);
        buffer.vertex(matrix, x1, y1, 0).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, x2, y2, 0).color(red, green, blue, alpha).next();
        
        tessellator.draw();
        RenderSystem.disableBlend();
    }
    
    public static void drawCircle(MatrixStack matrices, float centerX, float centerY, float radius, 
                                 int segments, int color) {
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        drawCircle(matrices, centerX, centerY, radius, segments, red, green, blue, alpha);
    }
    
    public static void drawCircle(MatrixStack matrices, float centerX, float centerY, float radius, 
                                 int segments, float red, float green, float blue, float alpha) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();
        
        buffer.begin(VertexFormat.DrawMode.TRIANGLE_FAN, VertexFormats.POSITION_COLOR);
        buffer.vertex(matrix, centerX, centerY, 0).color(red, green, blue, alpha).next();
        
        for (int i = 0; i <= segments; i++) {
            float angle = (float) (2.0 * Math.PI * i / segments);
            float x = centerX + radius * (float) Math.cos(angle);
            float y = centerY + radius * (float) Math.sin(angle);
            buffer.vertex(matrix, x, y, 0).color(red, green, blue, alpha).next();
        }
        
        tessellator.draw();
        RenderSystem.disableBlend();
    }
    
    // Utility methods for color manipulation
    public static int rgba(int r, int g, int b, int a) {
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    public static int rgb(int r, int g, int b) {
        return rgba(r, g, b, 255);
    }
    
    // Common colors
    public static final int WHITE = rgb(255, 255, 255);
    public static final int BLACK = rgb(0, 0, 0);
    public static final int RED = rgb(255, 0, 0);
    public static final int GREEN = rgb(0, 255, 0);
    public static final int BLUE = rgb(0, 0, 255);
    public static final int YELLOW = rgb(255, 255, 0);
    public static final int CYAN = rgb(0, 255, 255);
    public static final int MAGENTA = rgb(255, 0, 255);
}
