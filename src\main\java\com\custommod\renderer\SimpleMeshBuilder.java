package com.custommod.renderer;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.VertexFormat;
import net.minecraft.client.render.*;
import net.minecraft.client.util.math.MatrixStack;
import org.joml.Matrix4f;

public class SimpleMeshBuilder {
    private final Tessellator tessellator;
    private final BufferBuilder buffer;
    private boolean building = false;
    
    public SimpleMeshBuilder() {
        this.tessellator = Tessellator.getInstance();
        this.buffer = tessellator.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
    }
    
    public void begin() {
        if (building) {
            throw new IllegalStateException("Already building!");
        }
        building = true;
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
    }
    
    public void end() {
        if (!building) {
            throw new IllegalStateException("Not building!");
        }
        building = false;
        
        BufferRenderer.drawWithGlobalProgram(buffer.end());
        RenderSystem.disableBlend();
    }
    
    public SimpleMeshBuilder vertex(Matrix4f matrix, float x, float y, float z) {
        buffer.vertex(matrix, x, y, z);
        return this;
    }
    
    public SimpleMeshBuilder color(float r, float g, float b, float a) {
        buffer.color(r, g, b, a);
        return this;
    }
    
    public SimpleMeshBuilder color(int color) {
        float a = (float) (color >> 24 & 255) / 255.0F;
        float r = (float) (color >> 16 & 255) / 255.0F;
        float g = (float) (color >> 8 & 255) / 255.0F;
        float b = (float) (color & 255) / 255.0F;
        return color(r, g, b, a);
    }
    
    public void next() {
        buffer.next();
    }
    
    // Helper methods for common shapes
    public void drawRect(MatrixStack matrices, float x, float y, float width, float height, int color) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        vertex(matrix, x, y + height, 0).color(color).next();
        vertex(matrix, x + width, y + height, 0).color(color).next();
        vertex(matrix, x + width, y, 0).color(color).next();
        vertex(matrix, x, y, 0).color(color).next();
    }
    
    public void drawOutline(MatrixStack matrices, float x, float y, float width, float height, 
                           float lineWidth, int color) {
        // Top
        drawRect(matrices, x, y, width, lineWidth, color);
        // Bottom  
        drawRect(matrices, x, y + height - lineWidth, width, lineWidth, color);
        // Left
        drawRect(matrices, x, y, lineWidth, height, color);
        // Right
        drawRect(matrices, x + width - lineWidth, y, lineWidth, height, color);
    }
    
    // Utility methods for color manipulation
    public static int rgba(int r, int g, int b, int a) {
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    public static int rgb(int r, int g, int b) {
        return rgba(r, g, b, 255);
    }
    
    // Common colors
    public static final int WHITE = rgb(255, 255, 255);
    public static final int BLACK = rgb(0, 0, 0);
    public static final int RED = rgb(255, 0, 0);
    public static final int GREEN = rgb(0, 255, 0);
    public static final int BLUE = rgb(0, 0, 255);
    public static final int YELLOW = rgb(255, 255, 0);
    public static final int CYAN = rgb(0, 255, 255);
    public static final int MAGENTA = rgb(255, 0, 255);
}
