package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.*;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;

import java.util.ArrayList;
import java.util.List;

public class ModMenuScreen extends Screen {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private TextFieldWidget searchField;
    private List<FeatureButton> featureButtons = new ArrayList<>();
    private List<FeatureButton> filteredButtons = new ArrayList<>();
    private int scrollOffset = 0;
    private static final int BUTTON_HEIGHT = 20;
    private static final int BUTTON_SPACING = 25;
    private static final int BUTTONS_PER_PAGE = 10;
    
    public ModMenuScreen() {
        super(Text.literal("CustomMod Menu"));
        initializeFeatureButtons();
    }
    
    private void initializeFeatureButtons() {
        CustomModClient client = CustomModClient.getInstance();
        
        // Freecam
        featureButtons.add(new FeatureButton("Freecam", "Toggle freecam mode", 
            client.getFreecamManager()::isEnabled, 
            client.getFreecamManager()::toggle,
            () -> new FreecamConfigScreen(this)));
        
        // Item Highlight
        featureButtons.add(new FeatureButton("Item Highlight", "Highlight valuable items", 
            client.getItemHighlightManager()::isEnabled, 
            client.getItemHighlightManager()::toggle,
            () -> new ItemHighlightConfigScreen(this)));
        
        // Player Highlight
        featureButtons.add(new FeatureButton("Player Highlight", "Highlight players", 
            client.getPlayerHighlightManager()::isEnabled, 
            client.getPlayerHighlightManager()::toggle,
            () -> new PlayerHighlightConfigScreen(this)));
        
        // Target HUD
        featureButtons.add(new FeatureButton("Target HUD", "Show target information", 
            client.getTargetHudManager()::isEnabled, 
            client.getTargetHudManager()::toggle,
            () -> new TargetHudConfigScreen(this)));
        
        // ESP
        featureButtons.add(new FeatureButton("ESP", "Entity highlighting", 
            client.getEspManager()::isEnabled, 
            client.getEspManager()::toggle,
            () -> new EspConfigScreen(this)));
        
        // Block ESP
        featureButtons.add(new FeatureButton("Block ESP", "Highlight specific blocks", 
            client.getBlockEspManager()::isEnabled, 
            client.getBlockEspManager()::toggle,
            () -> new BlockEspConfigScreen(this)));
        
        // Entity ESP
        featureButtons.add(new FeatureButton("Entity ESP", "Highlight items and entities", 
            client.getEntityEspManager()::isEnabled, 
            client.getEntityEspManager()::toggle,
            () -> new EntityEspConfigScreen(this)));
        
        filteredButtons.addAll(featureButtons);
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Search field
        searchField = new TextFieldWidget(this.textRenderer, this.width / 2 - 100, 30, 200, 20, Text.literal("Search..."));
        searchField.setChangedListener(this::onSearchChanged);
        this.addSelectableChild(searchField);
        
        // Close button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Close"), button -> this.close())
            .dimensions(this.width / 2 - 50, this.height - 30, 100, 20)
            .build());
        
        updateButtons();
    }
    
    private void onSearchChanged(String search) {
        filteredButtons.clear();
        String searchLower = search.toLowerCase();
        
        if (searchLower.isEmpty()) {
            filteredButtons.addAll(featureButtons);
        } else {
            for (FeatureButton button : featureButtons) {
                if (button.name.toLowerCase().contains(searchLower) || 
                    button.description.toLowerCase().contains(searchLower)) {
                    filteredButtons.add(button);
                }
            }
        }
        
        scrollOffset = 0;
        updateButtons();
    }
    
    private void updateButtons() {
        // Clear existing buttons
        this.clearChildren();
        this.addSelectableChild(searchField);
        
        // Add close button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Close"), button -> this.close())
            .dimensions(this.width / 2 - 50, this.height - 30, 100, 20)
            .build());
        
        // Add feature buttons
        int startY = 60;
        int buttonWidth = 200;
        int configButtonWidth = 60;
        
        for (int i = scrollOffset; i < Math.min(scrollOffset + BUTTONS_PER_PAGE, filteredButtons.size()); i++) {
            FeatureButton feature = filteredButtons.get(i);
            int y = startY + (i - scrollOffset) * BUTTON_SPACING;
            
            // Toggle button
            String buttonText = feature.name + ": " + (feature.isEnabled.get() ? "ON" : "OFF");
            ButtonWidget toggleButton = ButtonWidget.builder(Text.literal(buttonText), button -> {
                feature.toggle.run();
                updateButtons(); // Refresh to update button text
            }).dimensions(this.width / 2 - buttonWidth / 2, y, buttonWidth - configButtonWidth - 5, BUTTON_HEIGHT).build();
            
            this.addDrawableChild(toggleButton);
            
            // Config button
            if (feature.configScreen != null) {
                ButtonWidget configButton = ButtonWidget.builder(Text.literal("Config"), button -> {
                    mc.setScreen(feature.configScreen.get());
                }).dimensions(this.width / 2 + buttonWidth / 2 - configButtonWidth, y, configButtonWidth, BUTTON_HEIGHT).build();
                
                this.addDrawableChild(configButton);
            }
        }
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        // Title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 10, 0xFFFFFF);
        
        // Search field background
        context.fill(searchField.getX() - 2, searchField.getY() - 2, 
                    searchField.getX() + searchField.getWidth() + 2, 
                    searchField.getY() + searchField.getHeight() + 2, 0xFF000000);
        
        super.render(context, mouseX, mouseY, delta);
        
        // Draw feature descriptions
        int startY = 60;
        for (int i = scrollOffset; i < Math.min(scrollOffset + BUTTONS_PER_PAGE, filteredButtons.size()); i++) {
            FeatureButton feature = filteredButtons.get(i);
            int y = startY + (i - scrollOffset) * BUTTON_SPACING + BUTTON_HEIGHT + 2;
            
            context.drawText(this.textRenderer, feature.description, this.width / 2 - 100, y, 0xAAAAAA, false);
        }
        
        // Scroll indicators
        if (scrollOffset > 0) {
            context.drawCenteredTextWithShadow(this.textRenderer, "↑ Scroll Up", this.width / 2, startY - 15, 0xFFFFFF);
        }
        
        if (scrollOffset + BUTTONS_PER_PAGE < filteredButtons.size()) {
            int endY = startY + BUTTONS_PER_PAGE * BUTTON_SPACING;
            context.drawCenteredTextWithShadow(this.textRenderer, "↓ Scroll Down", this.width / 2, endY, 0xFFFFFF);
        }
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        if (verticalAmount > 0 && scrollOffset > 0) {
            scrollOffset--;
            updateButtons();
            return true;
        } else if (verticalAmount < 0 && scrollOffset + BUTTONS_PER_PAGE < filteredButtons.size()) {
            scrollOffset++;
            updateButtons();
            return true;
        }
        return super.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (searchField.isFocused()) {
            return searchField.keyPressed(keyCode, scanCode, modifiers);
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    @Override
    public boolean charTyped(char chr, int modifiers) {
        if (searchField.isFocused()) {
            return searchField.charTyped(chr, modifiers);
        }
        return super.charTyped(chr, modifiers);
    }
    
    public void toggle() {
        if (mc.currentScreen == this) {
            mc.setScreen(null);
        } else {
            mc.setScreen(this);
        }
    }
    
    private static class FeatureButton {
        final String name;
        final String description;
        final java.util.function.Supplier<Boolean> isEnabled;
        final Runnable toggle;
        final java.util.function.Supplier<Screen> configScreen;
        
        public FeatureButton(String name, String description, 
                           java.util.function.Supplier<Boolean> isEnabled, 
                           Runnable toggle,
                           java.util.function.Supplier<Screen> configScreen) {
            this.name = name;
            this.description = description;
            this.isEnabled = isEnabled;
            this.toggle = toggle;
            this.configScreen = configScreen;
        }
    }
}
