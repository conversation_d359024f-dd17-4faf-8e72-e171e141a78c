package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.entity.Entity;
import net.minecraft.util.math.Vec3d;

public class FreecamManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    private boolean wasFlying = false;
    private Vec3d originalPos = null;
    private float originalYaw = 0;
    private float originalPitch = 0;
    private Entity freecamEntity = null;
    private float lastHealth = 0;
    
    public FreecamManager() {
    }
    
    public void toggle() {
        if (enabled) {
            disable();
        } else {
            enable();
        }
    }
    
    public void enable() {
        if (mc.player == null || mc.world == null) return;
        
        enabled = true;
        ClientPlayerEntity player = mc.player;
        
        // Store original state
        originalPos = player.getPos();
        originalYaw = player.getYaw();
        originalPitch = player.getPitch();
        wasFlying = player.getAbilities().flying;
        lastHealth = player.getHealth();
        
        // Enable flying for freecam
        player.getAbilities().flying = true;
        player.getAbilities().allowFlying = true;
        
        // Create freecam entity (invisible clone)
        freecamEntity = new FreecamEntity(mc.world, player);
        
        CustomModClient.LOGGER.info("Freecam enabled");
        updateConfig();
    }
    
    public void disable() {
        if (mc.player == null || !enabled) return;
        
        enabled = false;
        ClientPlayerEntity player = mc.player;
        
        // Restore original state
        if (originalPos != null) {
            player.setPosition(originalPos);
            player.setYaw(originalYaw);
            player.setPitch(originalPitch);
        }
        
        // Restore flying state
        player.getAbilities().flying = wasFlying;
        if (!wasFlying) {
            player.getAbilities().allowFlying = false;
        }
        
        // Remove freecam entity
        if (freecamEntity != null) {
            freecamEntity.discard();
            freecamEntity = null;
        }
        
        CustomModClient.LOGGER.info("Freecam disabled");
        updateConfig();
    }
    
    public void tick() {
        if (!enabled || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        
        // Check for damage if damage-sensitive mode is enabled
        if (config.freecamDamageSensitive) {
            float currentHealth = mc.player.getHealth();
            if (currentHealth < lastHealth) {
                // Player took damage, disable freecam
                disable();
                return;
            }
            lastHealth = currentHealth;
        }
        
        // Handle freecam movement
        handleFreecamMovement();
    }
    
    private void handleFreecamMovement() {
        if (mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        ClientPlayerEntity player = mc.player;
        
        // Adjust movement speed based on config
        float speed = config.freecamSpeed;
        
        // Apply speed multiplier to flying speed
        player.getAbilities().setFlySpeed(0.05f * speed);
        
        // Prevent fall damage while in freecam
        player.fallDistance = 0;
        
        // Keep player invisible/undetectable
        if (freecamEntity != null) {
            // Update freecam entity position to match player
            freecamEntity.setPosition(player.getPos());
            freecamEntity.setYaw(player.getYaw());
            freecamEntity.setPitch(player.getPitch());
        }
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        if (enabled) {
            enable();
        } else {
            disable();
        }
    }
    
    public boolean isDamageSensitive() {
        return CustomModClient.getInstance().getConfigManager().getConfig().freecamDamageSensitive;
    }
    
    public void setDamageSensitive(boolean damageSensitive) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.freecamDamageSensitive = damageSensitive;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public float getSpeed() {
        return CustomModClient.getInstance().getConfigManager().getConfig().freecamSpeed;
    }
    
    public void setSpeed(float speed) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.freecamSpeed = Math.max(0.1f, Math.min(10.0f, speed));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.freecamEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    // Inner class for freecam entity
    private static class FreecamEntity extends Entity {
        public FreecamEntity(net.minecraft.world.World world, ClientPlayerEntity player) {
            super(net.minecraft.entity.EntityType.PLAYER, world);
            this.setPosition(player.getPos());
            this.setYaw(player.getYaw());
            this.setPitch(player.getPitch());
            this.setInvisible(true);
            this.setInvulnerable(true);
            this.noClip = true;
        }
        
        @Override
        protected void initDataTracker() {
            // Empty implementation
        }
        
        @Override
        protected void readCustomDataFromNbt(net.minecraft.nbt.NbtCompound nbt) {
            // Empty implementation
        }
        
        @Override
        protected void writeCustomDataToNbt(net.minecraft.nbt.NbtCompound nbt) {
            // Empty implementation
        }
        
        @Override
        public void tick() {
            // Prevent normal entity ticking
        }
        
        @Override
        public boolean shouldRender(double distance) {
            return false; // Never render
        }
        
        @Override
        public boolean isInvisible() {
            return true;
        }

        @Override
        public boolean damage(net.minecraft.server.world.ServerWorld world, net.minecraft.entity.damage.DamageSource source, float amount) {
            return false; // Cannot be damaged
        }
    }
}
