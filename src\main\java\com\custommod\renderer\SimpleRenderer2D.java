package com.custommod.renderer;

import com.custommod.util.Color;
import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.client.render.BufferBuilder;
import net.minecraft.client.render.BufferRenderer;
import net.minecraft.client.render.GameRenderer;
import net.minecraft.client.render.Tessellator;
import net.minecraft.client.render.VertexFormat;
import net.minecraft.client.render.VertexFormats;
import net.minecraft.client.util.math.MatrixStack;
import org.joml.Matrix4f;

public class SimpleRenderer2D {
    
    public static void begin() {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
    }
    
    public static void end() {
        RenderSystem.disableBlend();
    }
    
    public static void drawRect(MatrixStack matrices, float x, float y, float width, float height, Color color) {
        drawRect(matrices, x, y, width, height, color.toInt());
    }
    
    public static void drawRect(MatrixStack matrices, float x, float y, float width, float height, int color) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        
        buffer.vertex(matrix, x, y + height, 0).color(red, green, blue, alpha);
        buffer.vertex(matrix, x + width, y + height, 0).color(red, green, blue, alpha);
        buffer.vertex(matrix, x + width, y, 0).color(red, green, blue, alpha);
        buffer.vertex(matrix, x, y, 0).color(red, green, blue, alpha);
        
        BufferRenderer.drawWithGlobalProgram(buffer.end());
    }
    
    public static void drawOutline(MatrixStack matrices, float x, float y, float width, float height, 
                                  float lineWidth, Color color) {
        drawOutline(matrices, x, y, width, height, lineWidth, color.toInt());
    }
    
    public static void drawOutline(MatrixStack matrices, float x, float y, float width, float height, 
                                  float lineWidth, int color) {
        // Top
        drawRect(matrices, x, y, width, lineWidth, color);
        // Bottom
        drawRect(matrices, x, y + height - lineWidth, width, lineWidth, color);
        // Left
        drawRect(matrices, x, y, lineWidth, height, color);
        // Right
        drawRect(matrices, x + width - lineWidth, y, lineWidth, height, color);
    }
    
    public static void drawLine(MatrixStack matrices, float x1, float y1, float x2, float y2, Color color) {
        drawLine(matrices, x1, y1, x2, y2, color.toInt());
    }
    
    public static void drawLine(MatrixStack matrices, float x1, float y1, float x2, float y2, int color) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.begin(VertexFormat.DrawMode.DEBUG_LINES, VertexFormats.POSITION_COLOR);
        
        buffer.vertex(matrix, x1, y1, 0).color(red, green, blue, alpha);
        buffer.vertex(matrix, x2, y2, 0).color(red, green, blue, alpha);
        
        BufferRenderer.drawWithGlobalProgram(buffer.end());
    }
    
    public static void drawCircle(MatrixStack matrices, float centerX, float centerY, float radius, 
                                 int segments, Color color) {
        drawCircle(matrices, centerX, centerY, radius, segments, color.toInt());
    }
    
    public static void drawCircle(MatrixStack matrices, float centerX, float centerY, float radius, 
                                 int segments, int color) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        
        float alpha = (float) (color >> 24 & 255) / 255.0F;
        float red = (float) (color >> 16 & 255) / 255.0F;
        float green = (float) (color >> 8 & 255) / 255.0F;
        float blue = (float) (color & 255) / 255.0F;
        
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.begin(VertexFormat.DrawMode.TRIANGLE_FAN, VertexFormats.POSITION_COLOR);
        
        buffer.vertex(matrix, centerX, centerY, 0).color(red, green, blue, alpha);
        
        for (int i = 0; i <= segments; i++) {
            float angle = (float) (2.0 * Math.PI * i / segments);
            float x = centerX + radius * (float) Math.cos(angle);
            float y = centerY + radius * (float) Math.sin(angle);
            buffer.vertex(matrix, x, y, 0).color(red, green, blue, alpha);
        }
        
        BufferRenderer.drawWithGlobalProgram(buffer.end());
    }
    
    // Utility methods for color manipulation
    public static int rgba(int r, int g, int b, int a) {
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    public static int rgb(int r, int g, int b) {
        return rgba(r, g, b, 255);
    }
}
