{"schemaVersion": 1, "id": "custommod", "version": "${version}", "name": "CustomMod", "description": "Advanced client-side mod with ESP, Freecam, and utility features", "authors": ["CustomMod Team"], "contact": {"homepage": "https://github.com/Fix85/CustomMod", "sources": "https://github.com/Fix85/CustomMod"}, "license": "MIT", "icon": "assets/custommod/icon.png", "environment": "client", "entrypoints": {"client": ["com.custommod.CustomModClient"]}, "mixins": ["custommod.mixins.json"], "depends": {"fabricloader": ">=${loader_version}", "fabric-api": "*", "minecraft": "${minecraft_version}"}, "suggests": {"another-mod": "*"}}