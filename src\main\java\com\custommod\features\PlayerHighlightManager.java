package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import com.custommod.util.RenderUtils;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderContext;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.AbstractClientPlayerEntity;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;

public class PlayerHighlightManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    
    public PlayerHighlightManager() {
    }
    
    public void toggle() {
        enabled = !enabled;
        updateConfig();
        CustomModClient.LOGGER.info("Player Highlight " + (enabled ? "enabled" : "disabled"));
    }
    
    public void render(WorldRenderContext context) {
        if (!enabled || mc.world == null || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        MatrixStack matrices = context.matrixStack();
        Vec3d cameraPos = context.camera().getPos();
        
        // Render highlights for all players
        for (AbstractClientPlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue; // Don't highlight self
            
            Vec3d playerPos = player.getPos();
            double distance = cameraPos.distanceTo(playerPos);
            
            if (distance > config.playerHighlightRange) continue;
            
            // Calculate alpha based on distance
            float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.playerHighlightRange));
            
            // Get player-specific color
            int color = getPlayerColor(player);
            
            matrices.push();
            matrices.translate(
                playerPos.x - cameraPos.x,
                playerPos.y - cameraPos.y,
                playerPos.z - cameraPos.z
            );
            
            // Draw outline box around player
            RenderUtils.drawBox(matrices, player.getBoundingBox().offset(-playerPos.x, -playerPos.y, -playerPos.z), 
                              color, alpha);
            
            // Draw filled box for better visibility
            RenderUtils.drawFilledBox(matrices, player.getBoundingBox().offset(-playerPos.x, -playerPos.y, -playerPos.z), 
                                    color, alpha * 0.2f);
            
            // Draw name tag above player
            if (distance < 32.0) {
                drawPlayerNameTag(matrices, player, color, alpha);
            }
            
            matrices.pop();
        }
    }
    
    private void drawPlayerNameTag(MatrixStack matrices, PlayerEntity player, int color, float alpha) {
        String name = player.getName().getString();
        float health = player.getHealth();
        float maxHealth = player.getMaxHealth();
        
        // Position name tag above player
        matrices.push();
        matrices.translate(0, player.getHeight() + 0.5, 0);
        
        // Make text face camera
        matrices.multiply(mc.gameRenderer.getCamera().getRotation());
        matrices.scale(-0.025f, -0.025f, 0.025f);
        
        // Draw background
        int textWidth = mc.textRenderer.getWidth(name);
        RenderUtils.drawFilledBox(matrices, 
            new net.minecraft.util.math.Box(-textWidth/2 - 2, -10, -1, textWidth/2 + 2, 2, 1),
            0x000000, alpha * 0.5f);
        
        // Draw player name
        RenderUtils.drawText(matrices, name, -textWidth/2, -8, color);
        
        // Draw health bar
        String healthText = String.format("%.1f/%.1f", health, maxHealth);
        int healthWidth = mc.textRenderer.getWidth(healthText);
        
        // Health bar background
        RenderUtils.drawFilledBox(matrices,
            new net.minecraft.util.math.Box(-healthWidth/2 - 2, 2, -1, healthWidth/2 + 2, 12, 1),
            0x000000, alpha * 0.5f);
        
        // Health bar
        float healthPercent = health / maxHealth;
        int healthColor = healthPercent > 0.6f ? 0xFF00FF00 : 
                         healthPercent > 0.3f ? 0xFFFFFF00 : 0xFFFF0000;
        
        RenderUtils.drawText(matrices, healthText, -healthWidth/2, 4, healthColor);
        
        matrices.pop();
    }
    
    private int getPlayerColor(PlayerEntity player) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        
        // Check if player is hostile/friendly based on various factors
        if (isHostilePlayer(player)) {
            return 0xFFFF0000; // Red for hostile
        } else if (isFriendlyPlayer(player)) {
            return 0xFF00FF00; // Green for friendly
        } else {
            return config.playerHighlightColor; // Default color
        }
    }
    
    private boolean isHostilePlayer(PlayerEntity player) {
        // Check if player is holding a weapon
        if (player.getMainHandStack().getItem().toString().contains("sword") ||
            player.getMainHandStack().getItem().toString().contains("axe") ||
            player.getMainHandStack().getItem().toString().contains("bow") ||
            player.getMainHandStack().getItem().toString().contains("crossbow") ||
            player.getMainHandStack().getItem().toString().contains("trident")) {
            return true;
        }
        
        // Check if player is wearing armor (might be geared for PvP)
        return player.getInventory().armor.stream().anyMatch(stack -> !stack.isEmpty());
    }
    
    private boolean isFriendlyPlayer(PlayerEntity player) {
        // Check if player is on the same team (if teams are available)
        if (mc.player != null && mc.player.getScoreboardTeam() != null && 
            player.getScoreboardTeam() != null) {
            return mc.player.getScoreboardTeam().equals(player.getScoreboardTeam());
        }
        
        // Check if player is holding non-combat items
        String itemName = player.getMainHandStack().getItem().toString();
        return itemName.contains("food") || itemName.contains("bread") || 
               itemName.contains("apple") || itemName.contains("carrot") ||
               itemName.contains("potato") || itemName.contains("book");
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateConfig();
    }
    
    public float getRange() {
        return CustomModClient.getInstance().getConfigManager().getConfig().playerHighlightRange;
    }
    
    public void setRange(float range) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.playerHighlightRange = Math.max(1.0f, Math.min(256.0f, range));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getColor() {
        return CustomModClient.getInstance().getConfigManager().getConfig().playerHighlightColor;
    }
    
    public void setColor(int color) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.playerHighlightColor = color;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.playerHighlightEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getPlayerCount() {
        if (mc.world == null || mc.player == null) return 0;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        Vec3d playerPos = mc.player.getPos();
        
        return (int) mc.world.getPlayers().stream()
            .filter(player -> player != mc.player)
            .filter(player -> playerPos.distanceTo(player.getPos()) <= config.playerHighlightRange)
            .count();
    }
}
