package com.custommod.keybinds;

import com.custommod.CustomModClient;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

public class KeybindManager {
    // Keybindings
    private KeyBinding freecamToggle;
    private KeyBinding playerHighlightToggle;
    private KeyBinding entityEspToggle;
    private KeyBinding modMenuToggle;
    private KeyBinding blockEspToggle;
    private KeyBinding itemHighlightToggle;
    private KeyBinding targetHudToggle;
    private KeyBinding espToggle;
    
    public KeybindManager() {
        registerKeybinds();
    }
    
    private void registerKeybinds() {
        // Initialize keybindings
        freecamToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.freecam_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_F,
            "category.custommod.general"
        ));
        
        playerHighlightToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.player_highlight_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_H,
            "category.custommod.general"
        ));
        
        entityEspToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.entity_esp_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_G,
            "category.custommod.general"
        ));
        
        modMenuToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.mod_menu_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_RIGHT_SHIFT,
            "category.custommod.general"
        ));
        
        blockEspToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.block_esp_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_B,
            "category.custommod.general"
        ));
        
        itemHighlightToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.item_highlight_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_I,
            "category.custommod.general"
        ));
        
        targetHudToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.target_hud_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_T,
            "category.custommod.general"
        ));
        
        espToggle = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.custommod.esp_toggle",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_E,
            "category.custommod.general"
        ));
    }
    
    public void tick() {
        CustomModClient client = CustomModClient.getInstance();
        
        // Handle freecam toggle
        if (freecamToggle.wasPressed()) {
            client.getFreecamManager().toggle();
        }
        
        // Handle player highlight toggle
        if (playerHighlightToggle.wasPressed()) {
            client.getPlayerHighlightManager().toggle();
        }
        
        // Handle entity ESP toggle
        if (entityEspToggle.wasPressed()) {
            client.getEntityEspManager().toggle();
        }
        
        // Handle mod menu toggle
        if (modMenuToggle.wasPressed()) {
            client.getModMenuScreen().toggle();
        }
        
        // Handle block ESP toggle
        if (blockEspToggle.wasPressed()) {
            client.getBlockEspManager().toggle();
        }
        
        // Handle item highlight toggle
        if (itemHighlightToggle.wasPressed()) {
            client.getItemHighlightManager().toggle();
        }
        
        // Handle target HUD toggle
        if (targetHudToggle.wasPressed()) {
            client.getTargetHudManager().toggle();
        }
        
        // Handle ESP toggle
        if (espToggle.wasPressed()) {
            client.getEspManager().toggle();
        }
    }
    
    // Getters for keybindings
    public KeyBinding getFreecamToggle() {
        return freecamToggle;
    }
    
    public KeyBinding getPlayerHighlightToggle() {
        return playerHighlightToggle;
    }
    
    public KeyBinding getEntityEspToggle() {
        return entityEspToggle;
    }
    
    public KeyBinding getModMenuToggle() {
        return modMenuToggle;
    }
    
    public KeyBinding getBlockEspToggle() {
        return blockEspToggle;
    }
    
    public KeyBinding getItemHighlightToggle() {
        return itemHighlightToggle;
    }
    
    public KeyBinding getTargetHudToggle() {
        return targetHudToggle;
    }
    
    public KeyBinding getEspToggle() {
        return espToggle;
    }
}
