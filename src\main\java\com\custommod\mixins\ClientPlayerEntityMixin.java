package com.custommod.mixins;

import com.custommod.CustomModClient;
import net.minecraft.client.network.ClientPlayerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayerEntity.class)
public class ClientPlayerEntityMixin {
    
    @Inject(method = "tick", at = @At("HEAD"))
    private void onTick(CallbackInfo ci) {
        // Handle freecam tick logic
        if (CustomModClient.getInstance() != null && 
            CustomModClient.getInstance().getFreecamManager().isEnabled()) {
            
            ClientPlayerEntity player = (ClientPlayerEntity) (Object) this;
            
            // Prevent fall damage in freecam
            player.fallDistance = 0;
            
            // Ensure no collision in freecam
            player.noClip = true;
        }
    }
    
    @Inject(method = "pushOutOfBlocks", at = @At("HEAD"), cancellable = true)
    private void onPushOutOfBlocks(double x, double z, CallbackInfo ci) {
        // Prevent being pushed out of blocks in freecam
        if (CustomModClient.getInstance() != null && 
            CustomModClient.getInstance().getFreecamManager().isEnabled()) {
            ci.cancel();
        }
    }
    
    @Inject(method = "updateNausea", at = @At("HEAD"), cancellable = true)
    private void onUpdateNausea(CallbackInfo ci) {
        // Prevent nausea effects in freecam
        if (CustomModClient.getInstance() != null && 
            CustomModClient.getInstance().getFreecamManager().isEnabled()) {
            ci.cancel();
        }
    }
}
