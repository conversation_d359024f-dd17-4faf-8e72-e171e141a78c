package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.EspManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.CheckboxWidget;
import net.minecraft.text.Text;

public class EspConfigScreen extends Screen {
    private final Screen parent;
    private final EspManager espManager;
    private CheckboxWidget playersCheckbox;
    private CheckboxWidget mobsCheckbox;
    private CheckboxWidget itemsCheckbox;
    private CheckboxWidget nametagsCheckbox;
    
    public EspConfigScreen(Screen parent) {
        super(Text.literal("ESP Configuration"));
        this.parent = parent;
        this.espManager = CustomModClient.getInstance().getEspManager();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Players checkbox
        playersCheckbox = CheckboxWidget.builder(Text.literal("Players"), this.textRenderer)
            .pos(this.width / 2 - 100, 80)
            .checked(espManager.isPlayersEnabled())
            .callback((checkbox, checked) -> espManager.setPlayersEnabled(checked))
            .build();
        this.addDrawableChild(playersCheckbox);
        
        // Mobs checkbox
        mobsCheckbox = CheckboxWidget.builder(Text.literal("Mobs"), this.textRenderer)
            .pos(this.width / 2 - 100, 105)
            .checked(espManager.isMobsEnabled())
            .callback((checkbox, checked) -> espManager.setMobsEnabled(checked))
            .build();
        this.addDrawableChild(mobsCheckbox);
        
        // Items checkbox
        itemsCheckbox = CheckboxWidget.builder(Text.literal("Items"), this.textRenderer)
            .pos(this.width / 2 - 100, 130)
            .checked(espManager.isItemsEnabled())
            .callback((checkbox, checked) -> espManager.setItemsEnabled(checked))
            .build();
        this.addDrawableChild(itemsCheckbox);
        
        // Nametags checkbox
        nametagsCheckbox = CheckboxWidget.builder(Text.literal("Nametags"), this.textRenderer)
            .pos(this.width / 2 - 100, 155)
            .checked(espManager.isNametagsEnabled())
            .callback((checkbox, checked) -> espManager.setNametagsEnabled(checked))
            .build();
        this.addDrawableChild(nametagsCheckbox);
        
        // Range controls
        ButtonWidget rangeDownButton = ButtonWidget.builder(Text.literal("-"), button -> {
            float newRange = Math.max(1.0f, espManager.getRange() - 10.0f);
            espManager.setRange(newRange);
        }).dimensions(this.width / 2 - 60, 185, 20, 20).build();
        this.addDrawableChild(rangeDownButton);
        
        ButtonWidget rangeUpButton = ButtonWidget.builder(Text.literal("+"), button -> {
            float newRange = Math.min(256.0f, espManager.getRange() + 10.0f);
            espManager.setRange(newRange);
        }).dimensions(this.width / 2 + 40, 185, 20, 20).build();
        this.addDrawableChild(rangeUpButton);
        
        // Back button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Back"), button -> {
            this.client.setScreen(parent);
        }).dimensions(this.width / 2 - 50, this.height - 30, 100, 20).build());
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, 0xFFFFFF);
        context.drawCenteredTextWithShadow(this.textRenderer, "Entity highlighting with nametags", this.width / 2, 40, 0xAAAAAA);
        
        // Range display
        String rangeText = String.format("Range: %.0f blocks", espManager.getRange());
        context.drawCenteredTextWithShadow(this.textRenderer, rangeText, this.width / 2, 190, 0xFFFFFF);
        
        super.render(context, mouseX, mouseY, delta);
    }
}
