package com.custommod;

import com.custommod.config.ConfigManager;
import com.custommod.features.*;
import com.custommod.gui.ModMenuScreen;
import com.custommod.keybinds.KeybindManager;
import com.custommod.util.RenderUtils;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CustomModClient implements ClientModInitializer {
    public static final String MOD_ID = "custommod";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);
    
    private static CustomModClient instance;
    
    // Feature managers
    private FreecamManager freecamManager;
    private ItemHighlightManager itemHighlightManager;
    private PlayerHighlightManager playerHighlightManager;
    private TargetHudManager targetHudManager;
    private EspManager espManager;
    private BlockEspManager blockEspManager;
    private EntityEspManager entityEspManager;
    
    // Core managers
    private ConfigManager configManager;
    private KeybindManager keybindManager;
    private ModMenuScreen modMenuScreen;
    
    @Override
    public void onInitializeClient() {
        instance = this;
        LOGGER.info("Initializing CustomMod...");
        
        // Initialize core managers
        configManager = new ConfigManager();
        keybindManager = new KeybindManager();
        
        // Initialize feature managers
        freecamManager = new FreecamManager();
        itemHighlightManager = new ItemHighlightManager();
        playerHighlightManager = new PlayerHighlightManager();
        targetHudManager = new TargetHudManager();
        espManager = new EspManager();
        blockEspManager = new BlockEspManager();
        entityEspManager = new EntityEspManager();
        
        // Initialize GUI
        modMenuScreen = new ModMenuScreen();
        
        // Register events
        registerEvents();
        
        LOGGER.info("CustomMod initialized successfully!");
    }
    
    private void registerEvents() {
        // Client tick events
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            keybindManager.tick();
            freecamManager.tick();
            targetHudManager.tick();
        });
        
        // World render events
        WorldRenderEvents.AFTER_ENTITIES.register((context) -> {
            RenderUtils.setupRender();
            
            if (espManager.isEnabled()) {
                espManager.render(context);
            }
            
            if (blockEspManager.isEnabled()) {
                blockEspManager.render(context);
            }
            
            if (entityEspManager.isEnabled()) {
                entityEspManager.render(context);
            }
            
            if (playerHighlightManager.isEnabled()) {
                playerHighlightManager.render(context);
            }
            
            if (itemHighlightManager.isEnabled()) {
                itemHighlightManager.render(context);
            }
            
            RenderUtils.cleanupRender();
        });
        
        // HUD render events
        HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
            if (targetHudManager.isEnabled()) {
                targetHudManager.render(drawContext, 1.0f);
            }
        });
    }
    
    public static CustomModClient getInstance() {
        return instance;
    }
    
    public FreecamManager getFreecamManager() {
        return freecamManager;
    }
    
    public ItemHighlightManager getItemHighlightManager() {
        return itemHighlightManager;
    }
    
    public PlayerHighlightManager getPlayerHighlightManager() {
        return playerHighlightManager;
    }
    
    public TargetHudManager getTargetHudManager() {
        return targetHudManager;
    }
    
    public EspManager getEspManager() {
        return espManager;
    }
    
    public BlockEspManager getBlockEspManager() {
        return blockEspManager;
    }
    
    public EntityEspManager getEntityEspManager() {
        return entityEspManager;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public KeybindManager getKeybindManager() {
        return keybindManager;
    }
    
    public ModMenuScreen getModMenuScreen() {
        return modMenuScreen;
    }
}
