package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import com.custommod.util.RenderUtils;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderContext;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.registry.Registries;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class BlockEspManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    
    public BlockEspManager() {
    }
    
    public void toggle() {
        enabled = !enabled;
        updateConfig();
        CustomModClient.LOGGER.info("Block ESP " + (enabled ? "enabled" : "disabled"));
    }
    
    public void render(WorldRenderContext context) {
        if (!enabled || mc.world == null || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        MatrixStack matrices = context.matrixStack();
        Vec3d cameraPos = context.camera().getPos();
        Vec3d playerPos = mc.player.getPos();
        
        int range = (int) config.blockEspRange;
        
        // Scan blocks in range
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = new BlockPos((int) playerPos.x + x, (int) playerPos.y + y, (int) playerPos.z + z);
                    
                    // Check if position is within range
                    double distance = playerPos.distanceTo(Vec3d.ofCenter(pos));
                    if (distance > config.blockEspRange) continue;
                    
                    BlockState blockState = mc.world.getBlockState(pos);
                    Block block = blockState.getBlock();
                    
                    String blockName = getBlockName(block);
                    
                    // Check if this block should be highlighted
                    if (config.blockEspBlocks.contains(blockName)) {
                        renderBlockESP(matrices, pos, cameraPos, blockName, config, distance);
                    }
                }
            }
        }
    }
    
    private void renderBlockESP(MatrixStack matrices, BlockPos pos, Vec3d cameraPos, 
                               String blockName, ConfigManager.ModConfig config, double distance) {
        
        float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.blockEspRange));
        int color = config.blockEspColors.getOrDefault(blockName, 0xFFFFFFFF);
        
        matrices.push();
        matrices.translate(
            pos.getX() - cameraPos.x,
            pos.getY() - cameraPos.y,
            pos.getZ() - cameraPos.z
        );
        
        // Draw block outline
        RenderUtils.drawBlockBox(matrices, BlockPos.ORIGIN, color, alpha);
        
        // Draw filled box for better visibility
        RenderUtils.drawFilledBox(matrices, 
            new net.minecraft.util.math.Box(0, 0, 0, 1, 1, 1), 
            color, alpha * 0.2f);
        
        matrices.pop();
    }
    
    private String getBlockName(Block block) {
        Identifier id = Registries.BLOCK.getId(block);
        return id.getPath();
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateConfig();
    }
    
    public float getRange() {
        return CustomModClient.getInstance().getConfigManager().getConfig().blockEspRange;
    }
    
    public void setRange(float range) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.blockEspRange = Math.max(1.0f, Math.min(64.0f, range));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public List<String> getTrackedBlocks() {
        return new ArrayList<>(CustomModClient.getInstance().getConfigManager().getConfig().blockEspBlocks);
    }
    
    public void addBlock(String blockName) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        
        // Validate block name
        if (!isValidBlockName(blockName)) {
            CustomModClient.LOGGER.warn("Invalid block name: " + blockName);
            return;
        }
        
        if (!config.blockEspBlocks.contains(blockName)) {
            config.blockEspBlocks.add(blockName);
            
            // Add default color if not present
            if (!config.blockEspColors.containsKey(blockName)) {
                config.blockEspColors.put(blockName, getDefaultBlockColor(blockName));
            }
            
            CustomModClient.getInstance().getConfigManager().saveConfig();
            CustomModClient.LOGGER.info("Added block to ESP: " + blockName);
        }
    }
    
    public void removeBlock(String blockName) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        
        if (config.blockEspBlocks.remove(blockName)) {
            config.blockEspColors.remove(blockName);
            CustomModClient.getInstance().getConfigManager().saveConfig();
            CustomModClient.LOGGER.info("Removed block from ESP: " + blockName);
        }
    }
    
    public void setBlockColor(String blockName, int color) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        
        if (config.blockEspBlocks.contains(blockName)) {
            config.blockEspColors.put(blockName, color);
            CustomModClient.getInstance().getConfigManager().saveConfig();
        }
    }
    
    public int getBlockColor(String blockName) {
        return CustomModClient.getInstance().getConfigManager().getConfig().blockEspColors
               .getOrDefault(blockName, 0xFFFFFFFF);
    }
    
    private boolean isValidBlockName(String blockName) {
        try {
            Identifier id = new Identifier("minecraft", blockName);
            return Registries.BLOCK.containsId(id);
        } catch (Exception e) {
            return false;
        }
    }
    
    private int getDefaultBlockColor(String blockName) {
        // Return appropriate colors based on block type
        if (blockName.contains("diamond")) {
            return 0xFF00FFFF; // Cyan
        } else if (blockName.contains("emerald")) {
            return 0xFF00FF00; // Green
        } else if (blockName.contains("gold")) {
            return 0xFFFFFF00; // Yellow
        } else if (blockName.contains("iron")) {
            return 0xFFC0C0C0; // Silver
        } else if (blockName.contains("coal")) {
            return 0xFF404040; // Dark gray
        } else if (blockName.contains("redstone")) {
            return 0xFFFF0000; // Red
        } else if (blockName.contains("lapis")) {
            return 0xFF0000FF; // Blue
        } else if (blockName.contains("ancient_debris")) {
            return 0xFFAA00AA; // Purple
        } else if (blockName.contains("copper")) {
            return 0xFFFF8800; // Orange
        } else if (blockName.contains("quartz")) {
            return 0xFFFFFFFF; // White
        } else {
            return 0xFFFF00FF; // Magenta (default)
        }
    }
    
    public void clearBlocks() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.blockEspBlocks.clear();
        config.blockEspColors.clear();
        CustomModClient.getInstance().getConfigManager().saveConfig();
        CustomModClient.LOGGER.info("Cleared all blocks from ESP");
    }
    
    public void addDefaultBlocks() {
        // Add commonly searched blocks
        addBlock("diamond_ore");
        addBlock("deepslate_diamond_ore");
        addBlock("emerald_ore");
        addBlock("deepslate_emerald_ore");
        addBlock("ancient_debris");
        addBlock("gold_ore");
        addBlock("deepslate_gold_ore");
        addBlock("nether_gold_ore");
        addBlock("iron_ore");
        addBlock("deepslate_iron_ore");
        addBlock("coal_ore");
        addBlock("deepslate_coal_ore");
        addBlock("redstone_ore");
        addBlock("deepslate_redstone_ore");
        addBlock("lapis_ore");
        addBlock("deepslate_lapis_ore");
        addBlock("copper_ore");
        addBlock("deepslate_copper_ore");
        addBlock("nether_quartz_ore");
        
        CustomModClient.LOGGER.info("Added default blocks to ESP");
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.blockEspEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getBlockCount() {
        if (!enabled || mc.world == null || mc.player == null) return 0;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        Vec3d playerPos = mc.player.getPos();
        int range = (int) config.blockEspRange;
        int count = 0;
        
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = new BlockPos((int) playerPos.x + x, (int) playerPos.y + y, (int) playerPos.z + z);
                    
                    double distance = playerPos.distanceTo(Vec3d.ofCenter(pos));
                    if (distance > config.blockEspRange) continue;
                    
                    BlockState blockState = mc.world.getBlockState(pos);
                    Block block = blockState.getBlock();
                    String blockName = getBlockName(block);
                    
                    if (config.blockEspBlocks.contains(blockName)) {
                        count++;
                    }
                }
            }
        }
        
        return count;
    }
}
