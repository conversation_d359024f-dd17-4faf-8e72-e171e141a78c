package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import com.custommod.util.RenderUtils;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderContext;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.decoration.ArmorStandEntity;
import net.minecraft.entity.decoration.ItemFrameEntity;
import net.minecraft.entity.vehicle.ChestMinecartEntity;
import net.minecraft.entity.vehicle.HopperMinecartEntity;
import net.minecraft.entity.vehicle.StorageMinecartEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.util.math.Vec3d;

public class EntityEspManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    
    public EntityEspManager() {
    }
    
    public void toggle() {
        enabled = !enabled;
        updateConfig();
        CustomModClient.LOGGER.info("Entity ESP " + (enabled ? "enabled" : "disabled"));
    }
    
    public void render(WorldRenderContext context) {
        if (!enabled || mc.world == null || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        MatrixStack matrices = context.matrixStack();
        Vec3d cameraPos = context.camera().getPos();
        
        // Render ESP for all relevant entities
        for (Entity entity : mc.world.getEntities()) {
            if (entity == mc.player) continue; // Don't ESP self
            
            Vec3d entityPos = entity.getPos();
            double distance = cameraPos.distanceTo(entityPos);
            
            if (distance > config.entityEspRange) continue;
            
            // Check if entity should be highlighted
            if (shouldHighlightEntity(entity)) {
                renderEntityESP(matrices, entity, cameraPos, config, distance);
            }
        }
    }
    
    private boolean shouldHighlightEntity(Entity entity) {
        // Highlight dropped items
        if (entity instanceof ItemEntity) {
            return true;
        }
        
        // Highlight storage entities
        if (entity instanceof ChestMinecartEntity || 
            entity instanceof HopperMinecartEntity || 
            entity instanceof StorageMinecartEntity) {
            return true;
        }
        
        // Highlight item frames
        if (entity instanceof ItemFrameEntity itemFrame) {
            return !itemFrame.getHeldItemStack().isEmpty();
        }
        
        // Highlight armor stands with items
        if (entity instanceof ArmorStandEntity armorStand) {
            return armorStand.getEquippedStack(net.minecraft.entity.EquipmentSlot.MAINHAND) != null ||
                   armorStand.getEquippedStack(net.minecraft.entity.EquipmentSlot.OFFHAND) != null ||
                   !armorStand.getArmorItems().iterator().next().isEmpty();
        }
        
        return false;
    }
    
    private void renderEntityESP(MatrixStack matrices, Entity entity, Vec3d cameraPos, 
                                ConfigManager.ModConfig config, double distance) {
        Vec3d entityPos = entity.getPos();
        float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.entityEspRange));
        
        matrices.push();
        matrices.translate(
            entityPos.x - cameraPos.x,
            entityPos.y - cameraPos.y,
            entityPos.z - cameraPos.z
        );
        
        // Get entity-specific color
        int color = getEntityColor(entity, config);
        
        // Draw entity box with glow effect
        renderGlowEffect(matrices, entity, color, alpha);
        
        // Draw entity outline
        RenderUtils.drawBox(matrices, entity.getBoundingBox().offset(-entityPos.x, -entityPos.y, -entityPos.z), 
                          color, alpha);
        
        // Draw filled box for better visibility
        RenderUtils.drawFilledBox(matrices, entity.getBoundingBox().offset(-entityPos.x, -entityPos.y, -entityPos.z), 
                                color, alpha * 0.15f);
        
        // Draw entity info if close enough
        if (distance < 16.0) {
            renderEntityInfo(matrices, entity, color, alpha);
        }
        
        matrices.pop();
    }
    
    private void renderGlowEffect(MatrixStack matrices, Entity entity, int color, float alpha) {
        // Create a glow effect by drawing multiple slightly larger boxes with decreasing alpha
        for (int i = 1; i <= 3; i++) {
            float scale = 1.0f + (i * 0.1f);
            float glowAlpha = alpha * (0.3f / i);
            
            matrices.push();
            matrices.scale(scale, scale, scale);
            matrices.translate(-entity.getWidth() * (scale - 1) / 2, 
                             -entity.getHeight() * (scale - 1) / 2, 
                             -entity.getWidth() * (scale - 1) / 2);
            
            RenderUtils.drawBox(matrices, 
                new net.minecraft.util.math.Box(0, 0, 0, entity.getWidth(), entity.getHeight(), entity.getWidth()), 
                color, glowAlpha);
            
            matrices.pop();
        }
    }
    
    private int getEntityColor(Entity entity, ConfigManager.ModConfig config) {
        if (entity instanceof ItemEntity itemEntity) {
            return getItemEntityColor(itemEntity);
        } else if (entity instanceof ChestMinecartEntity || 
                   entity instanceof StorageMinecartEntity) {
            return 0xFFFFFF00; // Yellow for storage
        } else if (entity instanceof ItemFrameEntity) {
            return 0xFF00FFFF; // Cyan for item frames
        } else if (entity instanceof ArmorStandEntity) {
            return 0xFFFF00FF; // Magenta for armor stands
        } else {
            return config.entityEspColor; // Default color
        }
    }
    
    private int getItemEntityColor(ItemEntity itemEntity) {
        ItemStack stack = itemEntity.getStack();
        
        // Color based on item rarity/value
        if (stack.hasEnchantments()) {
            return 0xFF8A2BE2; // Blue violet for enchanted items
        }
        
        String itemName = stack.getItem().toString();
        
        // Valuable items
        if (itemName.contains("diamond")) {
            return 0xFF00FFFF; // Cyan
        } else if (itemName.contains("emerald")) {
            return 0xFF00FF00; // Green
        } else if (itemName.contains("netherite") || itemName.contains("ancient_debris")) {
            return 0xFFAA00AA; // Purple
        } else if (itemName.contains("gold")) {
            return 0xFFFFFF00; // Yellow
        } else if (itemName.contains("iron")) {
            return 0xFFC0C0C0; // Silver
        } else {
            return 0xFFFFFFFF; // White for other items
        }
    }
    
    private void renderEntityInfo(MatrixStack matrices, Entity entity, int color, float alpha) {
        String info = getEntityInfo(entity);
        if (info.isEmpty()) return;
        
        matrices.push();
        matrices.translate(0, entity.getHeight() + 0.3, 0);
        matrices.multiply(mc.gameRenderer.getCamera().getRotation());
        matrices.scale(-0.02f, -0.02f, 0.02f);
        
        // Draw background
        int textWidth = mc.textRenderer.getWidth(info);
        RenderUtils.drawFilledBox(matrices, 
            new net.minecraft.util.math.Box(-textWidth/2 - 2, -8, -1, textWidth/2 + 2, 2, 1),
            0x000000, alpha * 0.7f);
        
        // Draw entity info
        RenderUtils.drawText(matrices, info, -textWidth/2, -6, color);
        
        matrices.pop();
    }
    
    private String getEntityInfo(Entity entity) {
        if (entity instanceof ItemEntity itemEntity) {
            ItemStack stack = itemEntity.getStack();
            int count = stack.getCount();
            String name = stack.getName().getString();
            return count > 1 ? name + " x" + count : name;
        } else if (entity instanceof ChestMinecartEntity) {
            return "Chest Minecart";
        } else if (entity instanceof HopperMinecartEntity) {
            return "Hopper Minecart";
        } else if (entity instanceof ItemFrameEntity itemFrame) {
            ItemStack stack = itemFrame.getHeldItemStack();
            return stack.isEmpty() ? "Empty Frame" : stack.getName().getString();
        } else if (entity instanceof ArmorStandEntity) {
            return "Armor Stand";
        } else {
            return entity.getName().getString();
        }
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateConfig();
    }
    
    public float getRange() {
        return CustomModClient.getInstance().getConfigManager().getConfig().entityEspRange;
    }
    
    public void setRange(float range) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.entityEspRange = Math.max(1.0f, Math.min(128.0f, range));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getColor() {
        return CustomModClient.getInstance().getConfigManager().getConfig().entityEspColor;
    }
    
    public void setColor(int color) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.entityEspColor = color;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.entityEspEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getEntityCount() {
        if (!enabled || mc.world == null || mc.player == null) return 0;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        Vec3d playerPos = mc.player.getPos();
        
        return (int) mc.world.getEntities().stream()
            .filter(entity -> entity != mc.player)
            .filter(this::shouldHighlightEntity)
            .filter(entity -> playerPos.distanceTo(entity.getPos()) <= config.entityEspRange)
            .count();
    }
}
