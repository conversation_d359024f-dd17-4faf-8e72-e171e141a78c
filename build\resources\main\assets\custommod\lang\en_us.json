{"category.custommod.general": "CustomMod", "key.custommod.freecam_toggle": "Toggle Freecam", "key.custommod.player_highlight_toggle": "Toggle Player Highlight", "key.custommod.entity_esp_toggle": "Toggle Entity ESP", "key.custommod.mod_menu_toggle": "Open Mod Menu", "key.custommod.block_esp_toggle": "Toggle Block ESP", "key.custommod.item_highlight_toggle": "Toggle Item Highlight", "key.custommod.target_hud_toggle": "Toggle Target HUD", "key.custommod.esp_toggle": "Toggle ESP", "custommod.gui.title": "CustomMod Settings", "custommod.gui.search": "Search features...", "custommod.gui.close": "Close", "custommod.gui.config": "Config", "custommod.gui.enabled": "Enabled", "custommod.gui.disabled": "Disabled", "custommod.freecam.title": "Freecam Settings", "custommod.freecam.damage_sensitive": "Damage Sensitive", "custommod.freecam.speed": "Speed", "custommod.freecam.description": "Freecam mode with optional damage detection", "custommod.item_highlight.title": "Item Highlight Settings", "custommod.item_highlight.range": "Range", "custommod.item_highlight.color": "Color", "custommod.item_highlight.description": "Highlight valuable items like in Nurik", "custommod.player_highlight.title": "Player Highlight Settings", "custommod.player_highlight.range": "Range", "custommod.player_highlight.color": "Color", "custommod.player_highlight.description": "Highlight players with keybind", "custommod.target_hud.title": "Target HUD Settings", "custommod.target_hud.x": "X Position", "custommod.target_hud.y": "Y Position", "custommod.target_hud.show_health": "Show Health", "custommod.target_hud.show_armor": "Show Armor", "custommod.target_hud.show_distance": "Show Distance", "custommod.target_hud.description": "Display target information (works on FUNTIME without invisibility)", "custommod.esp.title": "ESP Settings", "custommod.esp.range": "Range", "custommod.esp.players": "Players", "custommod.esp.mobs": "<PERSON><PERSON>", "custommod.esp.items": "Items", "custommod.esp.nametags": "Nametags", "custommod.esp.description": "Entity highlighting with nametags", "custommod.block_esp.title": "Block ESP Settings", "custommod.block_esp.range": "Range", "custommod.block_esp.add_block": "Add Block", "custommod.block_esp.remove_block": "Remove Block", "custommod.block_esp.block_name": "Block Name", "custommod.block_esp.description": "Highlight specific blocks", "custommod.entity_esp.title": "Entity ESP Settings", "custommod.entity_esp.range": "Range", "custommod.entity_esp.color": "Color", "custommod.entity_esp.description": "Glow effect for items and entities", "custommod.anti_detect.title": "Anti-Detect Settings", "custommod.anti_detect.enabled": "Anti-Detect Enabled", "custommod.anti_detect.randomize_packets": "Randomize Packet Timing", "custommod.anti_detect.limit_render": "<PERSON>it <PERSON>", "custommod.anti_detect.max_distance": "<PERSON>", "custommod.anti_detect.description": "Anti-detection features", "custommod.status.freecam_enabled": "Freecam enabled", "custommod.status.freecam_disabled": "Freecam disabled", "custommod.status.freecam_damage": "Freecam disabled due to damage", "custommod.status.item_highlight_enabled": "Item Highlight enabled", "custommod.status.item_highlight_disabled": "<PERSON>em Highlight disabled", "custommod.status.player_highlight_enabled": "Player Highlight enabled", "custommod.status.player_highlight_disabled": "Player Highlight disabled", "custommod.status.target_hud_enabled": "Target HUD enabled", "custommod.status.target_hud_disabled": "Target HUD disabled", "custommod.status.esp_enabled": "ESP enabled", "custommod.status.esp_disabled": "ESP disabled", "custommod.status.block_esp_enabled": "Block ESP enabled", "custommod.status.block_esp_disabled": "Block ESP disabled", "custommod.status.entity_esp_enabled": "Entity ESP enabled", "custommod.status.entity_esp_disabled": "En<PERSON>ty ESP disabled", "custommod.block_esp.added": "Added block: %s", "custommod.block_esp.removed": "Removed block: %s", "custommod.block_esp.invalid": "Invalid block name: %s", "custommod.block_esp.already_added": "Block already added: %s", "custommod.block_esp.not_found": "Block not found: %s", "custommod.command.help": "CustomMod Commands:", "custommod.command.toggle": "Toggle feature", "custommod.command.config": "Open configuration", "custommod.command.reload": "Reload configuration", "custommod.info.players_nearby": "Players nearby: %d", "custommod.info.entities_nearby": "Entities nearby: %d", "custommod.info.blocks_found": "Blocks found: %d", "custommod.info.distance": "Distance: %.1fm", "custommod.info.health": "Health: %.1f/%.1f", "custommod.info.armor": "Armor: %d"}