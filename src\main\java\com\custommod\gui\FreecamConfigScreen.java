package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.FreecamManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.CheckboxWidget;
import net.minecraft.text.Text;

public class FreecamConfigScreen extends Screen {
    private final Screen parent;
    private final FreecamManager freecamManager;
    private CheckboxWidget damageSensitiveCheckbox;
    
    public FreecamConfigScreen(Screen parent) {
        super(Text.literal("Freecam Configuration"));
        this.parent = parent;
        this.freecamManager = CustomModClient.getInstance().getFreecamManager();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Damage sensitive checkbox
        damageSensitiveCheckbox = CheckboxWidget.builder(Text.literal("Damage Sensitive Mode"), this.textRenderer)
            .pos(this.width / 2 - 100, 80)
            .checked(freecamManager.isDamageSensitive())
            .callback((checkbox, checked) -> freecamManager.setDamageSensitive(checked))
            .build();
        this.addDrawableChild(damageSensitiveCheckbox);
        
        // Speed controls
        float currentSpeed = freecamManager.getSpeed();
        ButtonWidget speedDownButton = ButtonWidget.builder(Text.literal("-"), button -> {
            float newSpeed = Math.max(0.1f, freecamManager.getSpeed() - 0.5f);
            freecamManager.setSpeed(newSpeed);
        }).dimensions(this.width / 2 - 60, 120, 20, 20).build();
        this.addDrawableChild(speedDownButton);
        
        ButtonWidget speedUpButton = ButtonWidget.builder(Text.literal("+"), button -> {
            float newSpeed = Math.min(10.0f, freecamManager.getSpeed() + 0.5f);
            freecamManager.setSpeed(newSpeed);
        }).dimensions(this.width / 2 + 40, 120, 20, 20).build();
        this.addDrawableChild(speedUpButton);
        
        // Back button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Back"), button -> {
            this.client.setScreen(parent);
        }).dimensions(this.width / 2 - 50, this.height - 30, 100, 20).build());
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        // Title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, 0xFFFFFF);
        
        // Description
        context.drawCenteredTextWithShadow(this.textRenderer, "Configure freecam behavior", this.width / 2, 40, 0xAAAAAA);
        
        // Damage sensitive description
        context.drawText(this.textRenderer, "When enabled, freecam will disable if you take damage", this.width / 2 - 100, 100, 0xAAAAAA, false);
        
        // Speed display
        String speedText = String.format("Speed: %.1fx", freecamManager.getSpeed());
        context.drawCenteredTextWithShadow(this.textRenderer, speedText, this.width / 2, 125, 0xFFFFFF);
        
        super.render(context, mouseX, mouseY, delta);
    }
}
