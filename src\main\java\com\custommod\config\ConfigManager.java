package com.custommod.config;

import com.custommod.CustomModClient;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConfigManager {
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final File CONFIG_DIR = new File(FabricLoader.getInstance().getConfigDir().toFile(), "custommod");
    private static final File CONFIG_FILE = new File(CONFIG_DIR, "config.json");
    
    private ModConfig config;
    
    public ConfigManager() {
        if (!CONFIG_DIR.exists()) {
            CONFIG_DIR.mkdirs();
        }
        loadConfig();
    }
    
    public void loadConfig() {
        if (CONFIG_FILE.exists()) {
            try (FileReader reader = new FileReader(CONFIG_FILE)) {
                config = GSON.fromJson(reader, ModConfig.class);
                if (config == null) {
                    config = new ModConfig();
                }
            } catch (IOException e) {
                CustomModClient.LOGGER.error("Failed to load config", e);
                config = new ModConfig();
            }
        } else {
            config = new ModConfig();
            saveConfig();
        }
    }
    
    public void saveConfig() {
        try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
            GSON.toJson(config, writer);
        } catch (IOException e) {
            CustomModClient.LOGGER.error("Failed to save config", e);
        }
    }
    
    public ModConfig getConfig() {
        return config;
    }
    
    public static class ModConfig {
        // Freecam settings
        public boolean freecamEnabled = false;
        public boolean freecamDamageSensitive = true;
        public float freecamSpeed = 1.0f;
        
        // Item highlight settings
        public boolean itemHighlightEnabled = false;
        public int itemHighlightColor = 0xFF00FF00;
        public float itemHighlightRange = 32.0f;
        
        // Player highlight settings
        public boolean playerHighlightEnabled = false;
        public int playerHighlightColor = 0xFFFF0000;
        public float playerHighlightRange = 64.0f;
        
        // Target HUD settings
        public boolean targetHudEnabled = false;
        public int targetHudX = 10;
        public int targetHudY = 10;
        public boolean targetHudShowHealth = true;
        public boolean targetHudShowArmor = true;
        public boolean targetHudShowDistance = true;
        
        // ESP settings
        public boolean espEnabled = false;
        public boolean espPlayers = true;
        public boolean espMobs = true;
        public boolean espItems = false;
        public boolean espNametags = true;
        public int espPlayerColor = 0xFFFF0000;
        public int espMobColor = 0xFF00FF00;
        public int espItemColor = 0xFF0000FF;
        public float espRange = 64.0f;
        
        // Block ESP settings
        public boolean blockEspEnabled = false;
        public List<String> blockEspBlocks = new ArrayList<>();
        public Map<String, Integer> blockEspColors = new HashMap<>();
        public float blockEspRange = 32.0f;
        
        // Entity ESP settings
        public boolean entityEspEnabled = false;
        public int entityEspColor = 0xFFFFFF00;
        public float entityEspRange = 32.0f;
        
        // Anti-detect settings
        public boolean antiDetectEnabled = true;
        public boolean randomizePacketTiming = true;
        public boolean limitRenderDistance = true;
        public float maxRenderDistance = 64.0f;
        
        // GUI settings
        public boolean guiEnabled = true;
        public int guiScale = 1;
        public boolean guiAnimations = true;
        
        public ModConfig() {
            // Initialize default block ESP blocks
            blockEspBlocks.add("diamond_ore");
            blockEspBlocks.add("emerald_ore");
            blockEspBlocks.add("ancient_debris");
            
            // Initialize default colors
            blockEspColors.put("diamond_ore", 0xFF00FFFF);
            blockEspColors.put("emerald_ore", 0xFF00FF00);
            blockEspColors.put("ancient_debris", 0xFFFF00FF);
        }
    }
}
