package com.custommod.mixins;

import com.custommod.CustomModClient;
import net.minecraft.entity.Entity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(Entity.class)
public class EntityMixin {
    
    @Inject(method = "isGlowing", at = @At("HEAD"), cancellable = true)
    private void onIsGlowing(CallbackInfoReturnable<Boolean> cir) {
        if (CustomModClient.getInstance() == null) return;
        
        Entity entity = (Entity) (Object) this;
        
        // Check if entity ESP is enabled and should glow
        if (CustomModClient.getInstance().getEntityEspManager().isEnabled()) {
            // Make items and relevant entities glow
            if (entity instanceof net.minecraft.entity.ItemEntity ||
                entity instanceof net.minecraft.entity.decoration.ItemFrameEntity ||
                entity instanceof net.minecraft.entity.vehicle.ChestMinecartEntity ||
                entity instanceof net.minecraft.entity.vehicle.StorageMinecartEntity) {
                cir.setReturnValue(true);
            }
        }
        
        // Check if player highlight is enabled
        if (CustomModClient.getInstance().getPlayerHighlightManager().isEnabled()) {
            if (entity instanceof net.minecraft.entity.player.PlayerEntity && 
                entity != net.minecraft.client.MinecraftClient.getInstance().player) {
                cir.setReturnValue(true);
            }
        }
    }
    
    @Inject(method = "getTeamColorValue", at = @At("HEAD"), cancellable = true)
    private void onGetTeamColorValue(CallbackInfoReturnable<Integer> cir) {
        if (CustomModClient.getInstance() == null) return;
        
        Entity entity = (Entity) (Object) this;
        
        // Override team color for ESP
        if (CustomModClient.getInstance().getEntityEspManager().isEnabled()) {
            if (entity instanceof net.minecraft.entity.ItemEntity) {
                cir.setReturnValue(CustomModClient.getInstance().getEntityEspManager().getColor());
            }
        }
        
        if (CustomModClient.getInstance().getPlayerHighlightManager().isEnabled()) {
            if (entity instanceof net.minecraft.entity.player.PlayerEntity && 
                entity != net.minecraft.client.MinecraftClient.getInstance().player) {
                cir.setReturnValue(CustomModClient.getInstance().getPlayerHighlightManager().getColor());
            }
        }
    }
}
