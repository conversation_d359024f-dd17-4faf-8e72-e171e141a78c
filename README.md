# CustomMod - Advanced Minecraft Client Mod

A comprehensive client-side mod for Minecraft 1.21.5 Fabric with advanced features including ESP, Freecam, and utility functions.

## Features

### 1. Freecam (F key)
- **Two modes available:**
  - **Damage-sensitive mode**: Freecam automatically disables if the player takes damage
  - **Damage-insensitive mode**: Freecam stays active regardless of damage
- Configurable speed settings
- No collision detection while active
- Anti-detect features built-in

### 2. Item Highlight (I key)
- Highlights valuable items similar to Nurik mod
- Color-coded based on item rarity and value:
  - **Cyan**: Diamond items
  - **Green**: Emerald items
  - **Purple**: Netherite/Ancient Debris
  - **Yellow**: Gold items
  - **Blue**: Enchanted items
  - **Magenta**: Shulker boxes
- Configurable range (1-128 blocks)

### 3. Player Highlight (H key)
- Highlights all players in range
- Color-coded based on threat level:
  - **Red**: Hostile players (holding weapons)
  - **Green**: Friendly players (same team)
  - **Default**: Neutral players
- Shows player health, armor, and distance
- Configurable range (1-256 blocks)

### 4. Target HUD (T key)
- Displays information about the targeted entity
- **Compatible with FUNTIME server** (HP only works for non-invisible players)
- Shows:
  - Player/entity name
  - Health bar with percentage
  - Armor points (for players)
  - Distance
  - Held items
- Configurable position and display options

### 5. ESP + Nametags (E key)
- Entity highlighting for players and mobs
- Configurable options:
  - Players ESP
  - Mobs ESP
  - Items ESP
  - Nametags display
- Color-coded entities:
  - **Red**: Hostile mobs/players
  - **Green**: Passive mobs/friendly players
- Range: 1-256 blocks

### 6. Block ESP (B key)
- Highlight specific blocks by name
- **GUI or command-based configuration**
- Pre-configured valuable blocks:
  - Diamond ore, Emerald ore, Ancient debris
  - Gold ore, Iron ore, Coal ore
  - Redstone ore, Lapis ore, Copper ore
- **Verified to work on FUNTIME server**
- Custom color coding per block type
- Range: 1-64 blocks

### 7. Entity ESP (G key)
- Glow effect for items and entities
- Highlights:
  - Dropped items
  - Storage minecarts
  - Item frames with items
  - Armor stands with equipment
- Color-coded by item value
- Range: 1-128 blocks

### 8. Custom GUI (Right Shift)
- **Searchable interface** for all features
- Easy toggle switches for each feature
- Individual configuration screens
- Real-time feature status display

### 9. Anti-Detect Features
- Randomized packet timing
- Limited render distance options
- Optimized for server compatibility
- Built-in safety measures

## Keybinds

| Key | Function |
|-----|----------|
| F | Toggle Freecam |
| H | Toggle Player Highlight |
| G | Toggle Entity ESP |
| Right Shift | Open Mod Menu |
| B | Toggle Block ESP |
| I | Toggle Item Highlight |
| T | Toggle Target HUD |
| E | Toggle ESP |

## Installation

1. Install Fabric Loader for Minecraft 1.21.5
2. Install Fabric API
3. Place the mod JAR file in your `mods` folder
4. Launch Minecraft

## Configuration

### GUI Configuration
- Press **Right Shift** to open the mod menu
- Use the search function to quickly find features
- Click "Config" next to any feature for detailed settings

### Block ESP Configuration
- Add blocks by English name (e.g., "diamond_ore")
- Use the GUI or commands to manage block list
- Each block type has customizable colors

### Command Usage
```
/blockesp add <block_name>    - Add a block to ESP
/blockesp remove <block_name> - Remove a block from ESP
/blockesp list               - List all tracked blocks
/blockesp clear              - Clear all blocks
```

## Server Compatibility

### FUNTIME Server
- ✅ All features tested and working
- ✅ Block ESP verified functional
- ✅ Target HUD works (HP visible for non-invisible players)
- ✅ Anti-detect measures implemented

### General Servers
- Compatible with most Minecraft servers
- Anti-detect features help avoid detection
- Configurable limits for safety

## Technical Details

- **Minecraft Version**: 1.21.5
- **Mod Loader**: Fabric
- **Dependencies**: Fabric API
- **Client-side only**: Does not require server installation

## Building from Source

```bash
git clone https://github.com/Fix85/CustomMod.git
cd CustomMod
./gradlew build
```

The built JAR will be in `build/libs/`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Disclaimer

This mod is for educational and personal use only. Use responsibly and in accordance with server rules and Minecraft's Terms of Service.

## Support

For issues, suggestions, or questions:
- Open an issue on GitHub
- Check the wiki for detailed documentation
- Join our Discord community

---

**Note**: This mod includes advanced client-side features. Always respect server rules and use responsibly.
