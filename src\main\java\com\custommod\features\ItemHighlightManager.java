package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import com.custommod.util.RenderUtils;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderContext;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.ItemEntity;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.Registries;
import net.minecraft.util.math.Vec3d;

import java.util.HashSet;
import java.util.Set;

public class ItemHighlightManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    
    // Valuable items to highlight (like in Nurik)
    private final Set<Item> valuableItems = new HashSet<>();
    
    public ItemHighlightManager() {
        initializeValuableItems();
    }
    
    private void initializeValuableItems() {
        // Diamonds and related
        valuableItems.add(Items.DIAMOND);
        valuableItems.add(Items.DIAMOND_ORE);
        valuableItems.add(Items.DEEPSLATE_DIAMOND_ORE);
        valuableItems.add(Items.DIAMOND_BLOCK);
        valuableItems.add(Items.DIAMOND_SWORD);
        valuableItems.add(Items.DIAMOND_PICKAXE);
        valuableItems.add(Items.DIAMOND_AXE);
        valuableItems.add(Items.DIAMOND_SHOVEL);
        valuableItems.add(Items.DIAMOND_HOE);
        valuableItems.add(Items.DIAMOND_HELMET);
        valuableItems.add(Items.DIAMOND_CHESTPLATE);
        valuableItems.add(Items.DIAMOND_LEGGINGS);
        valuableItems.add(Items.DIAMOND_BOOTS);
        
        // Emeralds and related
        valuableItems.add(Items.EMERALD);
        valuableItems.add(Items.EMERALD_ORE);
        valuableItems.add(Items.DEEPSLATE_EMERALD_ORE);
        valuableItems.add(Items.EMERALD_BLOCK);
        
        // Netherite and related
        valuableItems.add(Items.NETHERITE_INGOT);
        valuableItems.add(Items.NETHERITE_SCRAP);
        valuableItems.add(Items.ANCIENT_DEBRIS);
        valuableItems.add(Items.NETHERITE_BLOCK);
        valuableItems.add(Items.NETHERITE_SWORD);
        valuableItems.add(Items.NETHERITE_PICKAXE);
        valuableItems.add(Items.NETHERITE_AXE);
        valuableItems.add(Items.NETHERITE_SHOVEL);
        valuableItems.add(Items.NETHERITE_HOE);
        valuableItems.add(Items.NETHERITE_HELMET);
        valuableItems.add(Items.NETHERITE_CHESTPLATE);
        valuableItems.add(Items.NETHERITE_LEGGINGS);
        valuableItems.add(Items.NETHERITE_BOOTS);
        
        // Gold and related
        valuableItems.add(Items.GOLD_INGOT);
        valuableItems.add(Items.GOLD_ORE);
        valuableItems.add(Items.DEEPSLATE_GOLD_ORE);
        valuableItems.add(Items.NETHER_GOLD_ORE);
        valuableItems.add(Items.GOLD_BLOCK);
        valuableItems.add(Items.GOLDEN_APPLE);
        valuableItems.add(Items.ENCHANTED_GOLDEN_APPLE);
        
        // Iron and related
        valuableItems.add(Items.IRON_INGOT);
        valuableItems.add(Items.IRON_ORE);
        valuableItems.add(Items.DEEPSLATE_IRON_ORE);
        valuableItems.add(Items.IRON_BLOCK);
        
        // Rare items
        valuableItems.add(Items.NETHER_STAR);
        valuableItems.add(Items.DRAGON_EGG);
        valuableItems.add(Items.ELYTRA);
        valuableItems.add(Items.TOTEM_OF_UNDYING);
        valuableItems.add(Items.HEART_OF_THE_SEA);
        valuableItems.add(Items.NAUTILUS_SHELL);
        valuableItems.add(Items.TRIDENT);
        
        // Enchanted books and experience
        valuableItems.add(Items.ENCHANTED_BOOK);
        valuableItems.add(Items.EXPERIENCE_BOTTLE);
        
        // Shulker boxes
        valuableItems.add(Items.SHULKER_BOX);
        valuableItems.add(Items.WHITE_SHULKER_BOX);
        valuableItems.add(Items.ORANGE_SHULKER_BOX);
        valuableItems.add(Items.MAGENTA_SHULKER_BOX);
        valuableItems.add(Items.LIGHT_BLUE_SHULKER_BOX);
        valuableItems.add(Items.YELLOW_SHULKER_BOX);
        valuableItems.add(Items.LIME_SHULKER_BOX);
        valuableItems.add(Items.PINK_SHULKER_BOX);
        valuableItems.add(Items.GRAY_SHULKER_BOX);
        valuableItems.add(Items.LIGHT_GRAY_SHULKER_BOX);
        valuableItems.add(Items.CYAN_SHULKER_BOX);
        valuableItems.add(Items.PURPLE_SHULKER_BOX);
        valuableItems.add(Items.BLUE_SHULKER_BOX);
        valuableItems.add(Items.BROWN_SHULKER_BOX);
        valuableItems.add(Items.GREEN_SHULKER_BOX);
        valuableItems.add(Items.RED_SHULKER_BOX);
        valuableItems.add(Items.BLACK_SHULKER_BOX);
    }
    
    public void toggle() {
        enabled = !enabled;
        updateConfig();
        CustomModClient.LOGGER.info("Item Highlight " + (enabled ? "enabled" : "disabled"));
    }
    
    public void render(WorldRenderContext context) {
        if (!enabled || mc.world == null || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        MatrixStack matrices = context.matrixStack();
        Vec3d cameraPos = context.camera().getPos();
        
        // Render highlights for valuable items
        for (ItemEntity itemEntity : mc.world.getEntitiesByClass(ItemEntity.class, 
                mc.player.getBoundingBox().expand(config.itemHighlightRange), 
                entity -> entity != null && isValuableItem(entity.getStack()))) {
            
            Vec3d itemPos = itemEntity.getPos();
            double distance = cameraPos.distanceTo(itemPos);
            
            if (distance > config.itemHighlightRange) continue;
            
            // Get item-specific color
            int color = getItemColor(itemEntity.getStack());
            float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.itemHighlightRange));
            
            matrices.push();
            matrices.translate(
                itemPos.x - cameraPos.x,
                itemPos.y - cameraPos.y,
                itemPos.z - cameraPos.z
            );
            
            // Draw highlight box around item
            RenderUtils.drawBox(matrices, itemEntity.getBoundingBox().offset(-itemPos.x, -itemPos.y, -itemPos.z), 
                              color, alpha);
            
            // Draw filled box for better visibility
            RenderUtils.drawFilledBox(matrices, itemEntity.getBoundingBox().offset(-itemPos.x, -itemPos.y, -itemPos.z), 
                                    color, alpha * 0.3f);
            
            matrices.pop();
        }
    }
    
    private boolean isValuableItem(ItemStack stack) {
        if (stack.isEmpty()) return false;
        
        Item item = stack.getItem();
        
        // Check if item is in valuable items list
        if (valuableItems.contains(item)) return true;
        
        // Check if item is enchanted
        if (stack.hasEnchantments()) return true;
        
        // Check if item has custom name
        if (stack.getName() != stack.getItem().getName()) return true;
        
        return false;
    }
    
    private int getItemColor(ItemStack stack) {
        Item item = stack.getItem();
        
        // Netherite items - Purple
        if (item.toString().contains("netherite") || item == Items.ANCIENT_DEBRIS) {
            return 0xFFAA00AA;
        }
        
        // Diamond items - Cyan
        if (item.toString().contains("diamond")) {
            return 0xFF00FFFF;
        }
        
        // Emerald items - Green
        if (item.toString().contains("emerald")) {
            return 0xFF00FF00;
        }
        
        // Gold items - Yellow
        if (item.toString().contains("gold") || item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE) {
            return 0xFFFFFF00;
        }
        
        // Iron items - Gray
        if (item.toString().contains("iron")) {
            return 0xFFC0C0C0;
        }
        
        // Rare items - Red
        if (item == Items.NETHER_STAR || item == Items.DRAGON_EGG || item == Items.ELYTRA || 
            item == Items.TOTEM_OF_UNDYING || item == Items.TRIDENT) {
            return 0xFFFF0000;
        }
        
        // Enchanted items - Blue
        if (stack.hasEnchantments() || item == Items.ENCHANTED_BOOK) {
            return 0xFF0000FF;
        }
        
        // Shulker boxes - Magenta
        if (item.toString().contains("shulker_box")) {
            return 0xFFFF00FF;
        }
        
        // Default valuable item color - White
        return 0xFFFFFFFF;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateConfig();
    }
    
    public float getRange() {
        return CustomModClient.getInstance().getConfigManager().getConfig().itemHighlightRange;
    }
    
    public void setRange(float range) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.itemHighlightRange = Math.max(1.0f, Math.min(128.0f, range));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getColor() {
        return CustomModClient.getInstance().getConfigManager().getConfig().itemHighlightColor;
    }
    
    public void setColor(int color) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.itemHighlightColor = color;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.itemHighlightEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public void addValuableItem(Item item) {
        valuableItems.add(item);
    }
    
    public void removeValuableItem(Item item) {
        valuableItems.remove(item);
    }
    
    public Set<Item> getValuableItems() {
        return new HashSet<>(valuableItems);
    }
}
