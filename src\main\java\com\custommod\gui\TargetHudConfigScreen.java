package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.TargetHudManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.CheckboxWidget;
import net.minecraft.text.Text;

public class TargetHudConfigScreen extends Screen {
    private final Screen parent;
    private final TargetHudManager targetHudManager;
    private CheckboxWidget showHealthCheckbox;
    private CheckboxWidget showArmorCheckbox;
    private CheckboxWidget showDistanceCheckbox;
    
    public TargetHudConfigScreen(Screen parent) {
        super(Text.literal("Target HUD Configuration"));
        this.parent = parent;
        this.targetHudManager = CustomModClient.getInstance().getTargetHudManager();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Show health checkbox
        showHealthCheckbox = CheckboxWidget.builder(Text.literal("Show Health"), this.textRenderer)
            .pos(this.width / 2 - 100, 80)
            .checked(targetHudManager.isShowHealth())
            .callback((checkbox, checked) -> targetHudManager.setShowHealth(checked))
            .build();
        this.addDrawableChild(showHealthCheckbox);
        
        // Show armor checkbox
        showArmorCheckbox = CheckboxWidget.builder(Text.literal("Show Armor"), this.textRenderer)
            .pos(this.width / 2 - 100, 105)
            .checked(targetHudManager.isShowArmor())
            .callback((checkbox, checked) -> targetHudManager.setShowArmor(checked))
            .build();
        this.addDrawableChild(showArmorCheckbox);
        
        // Show distance checkbox
        showDistanceCheckbox = CheckboxWidget.builder(Text.literal("Show Distance"), this.textRenderer)
            .pos(this.width / 2 - 100, 130)
            .checked(targetHudManager.isShowDistance())
            .callback((checkbox, checked) -> targetHudManager.setShowDistance(checked))
            .build();
        this.addDrawableChild(showDistanceCheckbox);
        
        // Position controls
        ButtonWidget xDownButton = ButtonWidget.builder(Text.literal("X-"), button -> {
            int newX = Math.max(0, targetHudManager.getX() - 10);
            targetHudManager.setX(newX);
        }).dimensions(this.width / 2 - 80, 160, 30, 20).build();
        this.addDrawableChild(xDownButton);
        
        ButtonWidget xUpButton = ButtonWidget.builder(Text.literal("X+"), button -> {
            int newX = targetHudManager.getX() + 10;
            targetHudManager.setX(newX);
        }).dimensions(this.width / 2 - 45, 160, 30, 20).build();
        this.addDrawableChild(xUpButton);
        
        ButtonWidget yDownButton = ButtonWidget.builder(Text.literal("Y-"), button -> {
            int newY = Math.max(0, targetHudManager.getY() - 10);
            targetHudManager.setY(newY);
        }).dimensions(this.width / 2 + 15, 160, 30, 20).build();
        this.addDrawableChild(yDownButton);
        
        ButtonWidget yUpButton = ButtonWidget.builder(Text.literal("Y+"), button -> {
            int newY = targetHudManager.getY() + 10;
            targetHudManager.setY(newY);
        }).dimensions(this.width / 2 + 50, 160, 30, 20).build();
        this.addDrawableChild(yUpButton);
        
        // Back button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Back"), button -> {
            this.client.setScreen(parent);
        }).dimensions(this.width / 2 - 50, this.height - 30, 100, 20).build());
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, 0xFFFFFF);
        context.drawCenteredTextWithShadow(this.textRenderer, "Works on FUNTIME without invisibility", this.width / 2, 40, 0xAAAAAA);
        
        // Position display
        String posText = String.format("Position: X=%d, Y=%d", targetHudManager.getX(), targetHudManager.getY());
        context.drawCenteredTextWithShadow(this.textRenderer, posText, this.width / 2, 185, 0xFFFFFF);
        
        super.render(context, mouseX, mouseY, delta);
    }
}
