package com.custommod.util;

public class Color {
    public int r, g, b, a;

    public Color() {
        this(255, 255, 255, 255);
    }

    public Color(int r, int g, int b) {
        this.r = r;
        this.g = g;
        this.b = b;
        this.a = 255;
        validate();
    }

    public Color(int r, int g, int b, int a) {
        this.r = r;
        this.g = g;
        this.b = b;
        this.a = a;
        validate();
    }

    public Color(float r, float g, float b, float a) {
        this.r = (int)(r * 255);
        this.g = (int)(g * 255);
        this.b = (int)(b * 255);
        this.a = (int)(a * 255);
        validate();
    }

    public Color(int packed) {
        this.r = toRGBAR(packed);
        this.g = toRGBAG(packed);
        this.b = toRGBAB(packed);
        this.a = toRGBAA(packed);
    }

    public Color(Color color) {
        this.r = color.r;
        this.g = color.g;
        this.b = color.b;
        this.a = color.a;
    }
    
    public int toInt() {
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    public float getRed() {
        return r / 255.0f;
    }
    
    public float getGreen() {
        return g / 255.0f;
    }
    
    public float getBlue() {
        return b / 255.0f;
    }
    
    public float getAlpha() {
        return a / 255.0f;
    }
    
    public Color withAlpha(int alpha) {
        return new Color(r, g, b, alpha);
    }
    
    public Color withAlpha(float alpha) {
        return new Color(r, g, b, (int)(alpha * 255));
    }
    
    // Common colors
    public static final Color WHITE = new Color(255, 255, 255);
    public static final Color BLACK = new Color(0, 0, 0);
    public static final Color RED = new Color(255, 0, 0);
    public static final Color GREEN = new Color(0, 255, 0);
    public static final Color BLUE = new Color(0, 0, 255);
    public static final Color YELLOW = new Color(255, 255, 0);
    public static final Color CYAN = new Color(0, 255, 255);
    public static final Color MAGENTA = new Color(255, 0, 255);
    public static final Color GRAY = new Color(128, 128, 128);
    public static final Color LIGHT_GRAY = new Color(192, 192, 192);
    public static final Color DARK_GRAY = new Color(64, 64, 64);
    public static final Color ORANGE = new Color(255, 165, 0);
    public static final Color PINK = new Color(255, 192, 203);
    public static final Color PURPLE = new Color(128, 0, 128);
    
    @Override
    public String toString() {
        return String.format("Color{r=%d, g=%d, b=%d, a=%d}", r, g, b, a);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Color color = (Color) obj;
        return r == color.r && g == color.g && b == color.b && a == color.a;
    }
    
    @Override
    public int hashCode() {
        return toInt();
    }
}
