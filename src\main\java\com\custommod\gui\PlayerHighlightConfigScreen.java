package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.PlayerHighlightManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;

public class PlayerHighlightConfigScreen extends Screen {
    private final Screen parent;
    private final PlayerHighlightManager playerHighlightManager;
    
    public PlayerHighlightConfigScreen(Screen parent) {
        super(Text.literal("Player Highlight Configuration"));
        this.parent = parent;
        this.playerHighlightManager = CustomModClient.getInstance().getPlayerHighlightManager();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Range controls
        ButtonWidget rangeDownButton = ButtonWidget.builder(Text.literal("-"), button -> {
            float newRange = Math.max(1.0f, playerHighlightManager.getRange() - 10.0f);
            playerHighlightManager.setRange(newRange);
        }).dimensions(this.width / 2 - 60, 80, 20, 20).build();
        this.addDrawableChild(rangeDownButton);
        
        ButtonWidget rangeUpButton = ButtonWidget.builder(Text.literal("+"), button -> {
            float newRange = Math.min(256.0f, playerHighlightManager.getRange() + 10.0f);
            playerHighlightManager.setRange(newRange);
        }).dimensions(this.width / 2 + 40, 80, 20, 20).build();
        this.addDrawableChild(rangeUpButton);
        
        // Back button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Back"), button -> {
            this.client.setScreen(parent);
        }).dimensions(this.width / 2 - 50, this.height - 30, 100, 20).build());
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, 0xFFFFFF);
        context.drawCenteredTextWithShadow(this.textRenderer, "Highlight players with keybind activation", this.width / 2, 40, 0xAAAAAA);
        
        String rangeText = String.format("Range: %.0f blocks", playerHighlightManager.getRange());
        context.drawCenteredTextWithShadow(this.textRenderer, rangeText, this.width / 2, 85, 0xFFFFFF);
        
        int playerCount = playerHighlightManager.getPlayerCount();
        String countText = String.format("Players in range: %d", playerCount);
        context.drawCenteredTextWithShadow(this.textRenderer, countText, this.width / 2, 110, 0xAAAAAA);
        
        super.render(context, mouseX, mouseY, delta);
    }
}
