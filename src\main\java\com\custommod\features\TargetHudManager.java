package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.network.AbstractClientPlayerEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.Vec3d;

public class TargetHudManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    private LivingEntity currentTarget = null;
    private long lastTargetTime = 0;
    private static final long TARGET_TIMEOUT = 3000; // 3 seconds
    
    public TargetHudManager() {
    }
    
    public void toggle() {
        enabled = !enabled;
        updateConfig();
        CustomModClient.LOGGER.info("Target HUD " + (enabled ? "enabled" : "disabled"));
    }
    
    public void tick() {
        if (!enabled || mc.player == null) {
            currentTarget = null;
            return;
        }
        
        // Update current target based on crosshair
        updateTarget();
        
        // Clear target if too much time has passed
        if (currentTarget != null && System.currentTimeMillis() - lastTargetTime > TARGET_TIMEOUT) {
            currentTarget = null;
        }
    }
    
    private void updateTarget() {
        if (mc.crosshairTarget != null && mc.crosshairTarget.getType() == HitResult.Type.ENTITY) {
            EntityHitResult entityHit = (EntityHitResult) mc.crosshairTarget;
            if (entityHit.getEntity() instanceof LivingEntity livingEntity) {
                currentTarget = livingEntity;
                lastTargetTime = System.currentTimeMillis();
            }
        }
    }
    
    public void render(DrawContext drawContext, float tickDelta) {
        if (!enabled || currentTarget == null || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        
        // Check if target is still valid and visible
        if (!isTargetValid()) {
            currentTarget = null;
            return;
        }
        
        // Render the HUD
        renderTargetHud(drawContext, config);
    }
    
    private boolean isTargetValid() {
        if (currentTarget == null || currentTarget.isRemoved()) return false;
        
        // Check distance
        double distance = mc.player.getPos().distanceTo(currentTarget.getPos());
        if (distance > 64.0) return false;
        
        // For players, check if they're invisible (FUNTIME compatibility)
        if (currentTarget instanceof PlayerEntity player) {
            // On FUNTIME, we can only see HP if player is not invisible
            if (player.isInvisible()) {
                return false; // Can't get reliable HP data from invisible players
            }
        }
        
        return true;
    }
    
    private void renderTargetHud(DrawContext drawContext, ConfigManager.ModConfig config) {
        int x = config.targetHudX;
        int y = config.targetHudY;
        
        // Background
        drawContext.fill(x - 5, y - 5, x + 150, y + 60, 0x80000000);
        
        // Target name
        String name = currentTarget.getName().getString();
        drawContext.drawText(mc.textRenderer, name, x, y, 0xFFFFFF, true);
        
        int currentY = y + 12;
        
        // Health display
        if (config.targetHudShowHealth) {
            renderHealthInfo(drawContext, x, currentY, config);
            currentY += 12;
        }
        
        // Armor display (for players)
        if (config.targetHudShowArmor && currentTarget instanceof PlayerEntity player) {
            renderArmorInfo(drawContext, x, currentY, player);
            currentY += 12;
        }
        
        // Distance display
        if (config.targetHudShowDistance) {
            renderDistanceInfo(drawContext, x, currentY);
            currentY += 12;
        }
        
        // Additional info for players
        if (currentTarget instanceof AbstractClientPlayerEntity player) {
            renderPlayerInfo(drawContext, x, currentY, player);
        }
    }
    
    private void renderHealthInfo(DrawContext drawContext, int x, int y, ConfigManager.ModConfig config) {
        float health = currentTarget.getHealth();
        float maxHealth = currentTarget.getMaxHealth();
        float healthPercent = health / maxHealth;
        
        // Health text
        String healthText = String.format("Health: %.1f/%.1f", health, maxHealth);
        drawContext.drawText(mc.textRenderer, healthText, x, y, 0xFFFFFF, true);
        
        // Health bar
        int barWidth = 100;
        int barHeight = 4;
        int barX = x + 5;
        int barY = y + 10;
        
        // Background
        drawContext.fill(barX, barY, barX + barWidth, barY + barHeight, 0xFF333333);
        
        // Health bar color based on percentage
        int healthColor = healthPercent > 0.6f ? 0xFF00FF00 : 
                         healthPercent > 0.3f ? 0xFFFFFF00 : 0xFFFF0000;
        
        // Health bar fill
        int fillWidth = (int) (barWidth * healthPercent);
        drawContext.fill(barX, barY, barX + fillWidth, barY + barHeight, healthColor);
    }
    
    private void renderArmorInfo(DrawContext drawContext, int x, int y, PlayerEntity player) {
        int armor = player.getArmor();
        String armorText = String.format("Armor: %d", armor);
        
        int armorColor = armor > 15 ? 0xFF00FF00 : 
                        armor > 10 ? 0xFFFFFF00 : 
                        armor > 5 ? 0xFFFFA500 : 0xFFFF0000;
        
        drawContext.drawText(mc.textRenderer, armorText, x, y, armorColor, true);
    }
    
    private void renderDistanceInfo(DrawContext drawContext, int x, int y) {
        double distance = mc.player.getPos().distanceTo(currentTarget.getPos());
        String distanceText = String.format("Distance: %.1fm", distance);
        drawContext.drawText(mc.textRenderer, distanceText, x, y, 0xFFAAAAAA, true);
    }
    
    private void renderPlayerInfo(DrawContext drawContext, int x, int y, AbstractClientPlayerEntity player) {
        // Show held item
        ItemStack heldItem = player.getMainHandStack();
        if (!heldItem.isEmpty()) {
            String itemText = "Holding: " + heldItem.getName().getString();
            drawContext.drawText(mc.textRenderer, itemText, x, y, 0xFFCCCCCC, true);
        }
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateConfig();
    }
    
    public int getX() {
        return CustomModClient.getInstance().getConfigManager().getConfig().targetHudX;
    }
    
    public void setX(int x) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.targetHudX = Math.max(0, Math.min(mc.getWindow().getScaledWidth() - 160, x));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public int getY() {
        return CustomModClient.getInstance().getConfigManager().getConfig().targetHudY;
    }
    
    public void setY(int y) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.targetHudY = Math.max(0, Math.min(mc.getWindow().getScaledHeight() - 70, y));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isShowHealth() {
        return CustomModClient.getInstance().getConfigManager().getConfig().targetHudShowHealth;
    }
    
    public void setShowHealth(boolean showHealth) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.targetHudShowHealth = showHealth;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isShowArmor() {
        return CustomModClient.getInstance().getConfigManager().getConfig().targetHudShowArmor;
    }
    
    public void setShowArmor(boolean showArmor) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.targetHudShowArmor = showArmor;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isShowDistance() {
        return CustomModClient.getInstance().getConfigManager().getConfig().targetHudShowDistance;
    }
    
    public void setShowDistance(boolean showDistance) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.targetHudShowDistance = showDistance;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.targetHudEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public LivingEntity getCurrentTarget() {
        return currentTarget;
    }
    
    public void setTarget(LivingEntity target) {
        this.currentTarget = target;
        this.lastTargetTime = System.currentTimeMillis();
    }
}
