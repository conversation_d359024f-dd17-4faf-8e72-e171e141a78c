package com.custommod.features;

import com.custommod.CustomModClient;
import com.custommod.config.ConfigManager;
import com.custommod.util.RenderUtils;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderContext;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.AbstractClientPlayerEntity;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.passive.PassiveEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;

public class EspManager {
    private final MinecraftClient mc = MinecraftClient.getInstance();
    private boolean enabled = false;
    
    public EspManager() {
    }
    
    public void toggle() {
        enabled = !enabled;
        updateConfig();
        CustomModClient.LOGGER.info("ESP " + (enabled ? "enabled" : "disabled"));
    }
    
    public void render(WorldRenderContext context) {
        if (!enabled || mc.world == null || mc.player == null) return;
        
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        MatrixStack matrices = context.matrixStack();
        Vec3d cameraPos = context.camera().getPos();
        
        // Render ESP for all entities in range
        for (Entity entity : mc.world.getEntities()) {
            if (entity == mc.player) continue; // Don't ESP self
            
            Vec3d entityPos = entity.getPos();
            double distance = cameraPos.distanceTo(entityPos);
            
            if (distance > config.espRange) continue;
            
            // Check entity type and render accordingly
            if (entity instanceof PlayerEntity && config.espPlayers) {
                renderPlayerESP(matrices, (PlayerEntity) entity, cameraPos, config, distance);
            } else if (entity instanceof LivingEntity && !(entity instanceof PlayerEntity) && config.espMobs) {
                renderMobESP(matrices, (LivingEntity) entity, cameraPos, config, distance);
            } else if (entity instanceof ItemEntity && config.espItems) {
                renderItemESP(matrices, (ItemEntity) entity, cameraPos, config, distance);
            }
        }
    }
    
    private void renderPlayerESP(MatrixStack matrices, PlayerEntity player, Vec3d cameraPos, 
                                ConfigManager.ModConfig config, double distance) {
        Vec3d playerPos = player.getPos();
        float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.espRange));
        
        matrices.push();
        matrices.translate(
            playerPos.x - cameraPos.x,
            playerPos.y - cameraPos.y,
            playerPos.z - cameraPos.z
        );
        
        // Draw player box
        int color = getPlayerESPColor(player, config);
        RenderUtils.drawBox(matrices, player.getBoundingBox().offset(-playerPos.x, -playerPos.y, -playerPos.z), 
                          color, alpha);
        
        // Draw filled box for better visibility
        RenderUtils.drawFilledBox(matrices, player.getBoundingBox().offset(-playerPos.x, -playerPos.y, -playerPos.z), 
                                color, alpha * 0.15f);
        
        // Draw nametag if enabled
        if (config.espNametags && distance < 32.0) {
            renderPlayerNametag(matrices, player, color, alpha);
        }
        
        matrices.pop();
    }
    
    private void renderMobESP(MatrixStack matrices, LivingEntity mob, Vec3d cameraPos, 
                             ConfigManager.ModConfig config, double distance) {
        Vec3d mobPos = mob.getPos();
        float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.espRange));
        
        matrices.push();
        matrices.translate(
            mobPos.x - cameraPos.x,
            mobPos.y - cameraPos.y,
            mobPos.z - cameraPos.z
        );
        
        // Draw mob box
        int color = getMobESPColor(mob, config);
        RenderUtils.drawBox(matrices, mob.getBoundingBox().offset(-mobPos.x, -mobPos.y, -mobPos.z), 
                          color, alpha);
        
        // Draw filled box for better visibility
        RenderUtils.drawFilledBox(matrices, mob.getBoundingBox().offset(-mobPos.x, -mobPos.y, -mobPos.z), 
                                color, alpha * 0.15f);
        
        // Draw nametag if enabled
        if (config.espNametags && distance < 24.0) {
            renderMobNametag(matrices, mob, color, alpha);
        }
        
        matrices.pop();
    }
    
    private void renderItemESP(MatrixStack matrices, ItemEntity item, Vec3d cameraPos, 
                              ConfigManager.ModConfig config, double distance) {
        Vec3d itemPos = item.getPos();
        float alpha = Math.max(0.3f, 1.0f - (float)(distance / config.espRange));
        
        matrices.push();
        matrices.translate(
            itemPos.x - cameraPos.x,
            itemPos.y - cameraPos.y,
            itemPos.z - cameraPos.z
        );
        
        // Draw item box
        RenderUtils.drawBox(matrices, item.getBoundingBox().offset(-itemPos.x, -itemPos.y, -itemPos.z), 
                          config.espItemColor, alpha);
        
        // Draw nametag if enabled
        if (config.espNametags && distance < 16.0) {
            renderItemNametag(matrices, item, config.espItemColor, alpha);
        }
        
        matrices.pop();
    }
    
    private int getPlayerESPColor(PlayerEntity player, ConfigManager.ModConfig config) {
        // Check if player is hostile
        if (isHostilePlayer(player)) {
            return 0xFFFF0000; // Red for hostile players
        }
        
        // Check if player is friendly/teammate
        if (isFriendlyPlayer(player)) {
            return 0xFF00FF00; // Green for friendly players
        }
        
        // Default player color
        return config.espPlayerColor;
    }
    
    private int getMobESPColor(LivingEntity mob, ConfigManager.ModConfig config) {
        if (mob instanceof HostileEntity) {
            return 0xFFFF0000; // Red for hostile mobs
        } else if (mob instanceof PassiveEntity) {
            return 0xFF00FF00; // Green for passive mobs
        } else {
            return config.espMobColor; // Default mob color
        }
    }
    
    private boolean isHostilePlayer(PlayerEntity player) {
        // Check if player is holding a weapon
        String itemName = player.getMainHandStack().getItem().toString();
        return itemName.contains("sword") || itemName.contains("axe") || 
               itemName.contains("bow") || itemName.contains("crossbow") ||
               itemName.contains("trident");
    }
    
    private boolean isFriendlyPlayer(PlayerEntity player) {
        // Check if player is on the same team
        if (mc.player != null && mc.player.getScoreboardTeam() != null && 
            player.getScoreboardTeam() != null) {
            return mc.player.getScoreboardTeam().equals(player.getScoreboardTeam());
        }
        return false;
    }
    
    private void renderPlayerNametag(MatrixStack matrices, PlayerEntity player, int color, float alpha) {
        String name = player.getName().getString();
        float health = player.getHealth();
        float maxHealth = player.getMaxHealth();
        double distance = mc.player.getPos().distanceTo(player.getPos());
        
        matrices.push();
        matrices.translate(0, player.getHeight() + 0.5, 0);
        matrices.multiply(mc.gameRenderer.getCamera().getRotation());
        matrices.scale(-0.025f, -0.025f, 0.025f);
        
        // Draw background
        int textWidth = mc.textRenderer.getWidth(name);
        RenderUtils.drawFilledBox(matrices, 
            new net.minecraft.util.math.Box(-textWidth/2 - 2, -20, -1, textWidth/2 + 2, 5, 1),
            0x000000, alpha * 0.7f);
        
        // Draw player name
        RenderUtils.drawText(matrices, name, -textWidth/2, -18, color);
        
        // Draw health
        String healthText = String.format("%.1f HP", health);
        float healthPercent = health / maxHealth;
        int healthColor = healthPercent > 0.6f ? 0xFF00FF00 : 
                         healthPercent > 0.3f ? 0xFFFFFF00 : 0xFFFF0000;
        
        int healthWidth = mc.textRenderer.getWidth(healthText);
        RenderUtils.drawText(matrices, healthText, -healthWidth/2, -8, healthColor);
        
        // Draw distance
        String distanceText = String.format("%.1fm", distance);
        int distanceWidth = mc.textRenderer.getWidth(distanceText);
        RenderUtils.drawText(matrices, distanceText, -distanceWidth/2, 2, 0xFFAAAAAA);
        
        matrices.pop();
    }
    
    private void renderMobNametag(MatrixStack matrices, LivingEntity mob, int color, float alpha) {
        String name = mob.getName().getString();
        float health = mob.getHealth();
        double distance = mc.player.getPos().distanceTo(mob.getPos());
        
        matrices.push();
        matrices.translate(0, mob.getHeight() + 0.3, 0);
        matrices.multiply(mc.gameRenderer.getCamera().getRotation());
        matrices.scale(-0.02f, -0.02f, 0.02f);
        
        // Draw background
        int textWidth = mc.textRenderer.getWidth(name);
        RenderUtils.drawFilledBox(matrices, 
            new net.minecraft.util.math.Box(-textWidth/2 - 2, -15, -1, textWidth/2 + 2, 5, 1),
            0x000000, alpha * 0.7f);
        
        // Draw mob name
        RenderUtils.drawText(matrices, name, -textWidth/2, -13, color);
        
        // Draw health
        String healthText = String.format("%.1f HP", health);
        int healthWidth = mc.textRenderer.getWidth(healthText);
        RenderUtils.drawText(matrices, healthText, -healthWidth/2, -3, 0xFFFF0000);
        
        matrices.pop();
    }
    
    private void renderItemNametag(MatrixStack matrices, ItemEntity item, int color, float alpha) {
        String name = item.getStack().getName().getString();
        int count = item.getStack().getCount();
        
        matrices.push();
        matrices.translate(0, 0.5, 0);
        matrices.multiply(mc.gameRenderer.getCamera().getRotation());
        matrices.scale(-0.02f, -0.02f, 0.02f);
        
        String displayText = count > 1 ? name + " x" + count : name;
        int textWidth = mc.textRenderer.getWidth(displayText);
        
        // Draw background
        RenderUtils.drawFilledBox(matrices, 
            new net.minecraft.util.math.Box(-textWidth/2 - 2, -8, -1, textWidth/2 + 2, 2, 1),
            0x000000, alpha * 0.7f);
        
        // Draw item name
        RenderUtils.drawText(matrices, displayText, -textWidth/2, -6, color);
        
        matrices.pop();
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        updateConfig();
    }
    
    public float getRange() {
        return CustomModClient.getInstance().getConfigManager().getConfig().espRange;
    }
    
    public void setRange(float range) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.espRange = Math.max(1.0f, Math.min(256.0f, range));
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isPlayersEnabled() {
        return CustomModClient.getInstance().getConfigManager().getConfig().espPlayers;
    }
    
    public void setPlayersEnabled(boolean enabled) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.espPlayers = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isMobsEnabled() {
        return CustomModClient.getInstance().getConfigManager().getConfig().espMobs;
    }
    
    public void setMobsEnabled(boolean enabled) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.espMobs = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isItemsEnabled() {
        return CustomModClient.getInstance().getConfigManager().getConfig().espItems;
    }
    
    public void setItemsEnabled(boolean enabled) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.espItems = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    public boolean isNametagsEnabled() {
        return CustomModClient.getInstance().getConfigManager().getConfig().espNametags;
    }
    
    public void setNametagsEnabled(boolean enabled) {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.espNametags = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
    
    private void updateConfig() {
        ConfigManager.ModConfig config = CustomModClient.getInstance().getConfigManager().getConfig();
        config.espEnabled = enabled;
        CustomModClient.getInstance().getConfigManager().saveConfig();
    }
}
