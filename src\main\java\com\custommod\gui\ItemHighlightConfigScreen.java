package com.custommod.gui;

import com.custommod.CustomModClient;
import com.custommod.features.ItemHighlightManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;

public class ItemHighlightConfigScreen extends Screen {
    private final Screen parent;
    private final ItemHighlightManager itemHighlightManager;
    
    public ItemHighlightConfigScreen(Screen parent) {
        super(Text.literal("Item Highlight Configuration"));
        this.parent = parent;
        this.itemHighlightManager = CustomModClient.getInstance().getItemHighlightManager();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Range controls
        ButtonWidget rangeDownButton = ButtonWidget.builder(Text.literal("-"), button -> {
            float newRange = Math.max(1.0f, itemHighlightManager.getRange() - 5.0f);
            itemHighlightManager.setRange(newRange);
        }).dimensions(this.width / 2 - 60, 80, 20, 20).build();
        this.addDrawableChild(rangeDownButton);
        
        ButtonWidget rangeUpButton = ButtonWidget.builder(Text.literal("+"), button -> {
            float newRange = Math.min(128.0f, itemHighlightManager.getRange() + 5.0f);
            itemHighlightManager.setRange(newRange);
        }).dimensions(this.width / 2 + 40, 80, 20, 20).build();
        this.addDrawableChild(rangeUpButton);
        
        // Back button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Back"), button -> {
            this.client.setScreen(parent);
        }).dimensions(this.width / 2 - 50, this.height - 30, 100, 20).build());
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        this.renderBackground(context, mouseX, mouseY, delta);
        
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, 0xFFFFFF);
        context.drawCenteredTextWithShadow(this.textRenderer, "Highlights valuable items like in Nurik", this.width / 2, 40, 0xAAAAAA);
        
        String rangeText = String.format("Range: %.0f blocks", itemHighlightManager.getRange());
        context.drawCenteredTextWithShadow(this.textRenderer, rangeText, this.width / 2, 85, 0xFFFFFF);
        
        super.render(context, mouseX, mouseY, delta);
    }
}
