package com.custommod.util;

import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.render.*;
import net.minecraft.client.render.debug.DebugRenderer;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import org.joml.Matrix4f;

public class RenderUtils {
    private static final MinecraftClient mc = MinecraftClient.getInstance();
    
    public static void setupRender() {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.depthMask(false);
    }
    
    public static void cleanupRender() {
        RenderSystem.enableDepthTest();
        RenderSystem.depthMask(true);
        RenderSystem.disableBlend();
    }
    
    public static void drawBox(MatrixStack matrices, Box box, int color, float alpha) {
        float red = (color >> 16 & 255) / 255.0f;
        float green = (color >> 8 & 255) / 255.0f;
        float blue = (color & 255) / 255.0f;
        
        drawBox(matrices, box, red, green, blue, alpha);
    }
    
    public static void drawBox(MatrixStack matrices, Box box, float red, float green, float blue, float alpha) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();
        
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        buffer.begin(VertexFormat.DrawMode.DEBUG_LINES, VertexFormats.POSITION_COLOR);
        
        // Draw box outline
        drawBoxOutline(buffer, matrix, box, red, green, blue, alpha);
        
        tessellator.draw();
    }
    
    public static void drawFilledBox(MatrixStack matrices, Box box, int color, float alpha) {
        float red = (color >> 16 & 255) / 255.0f;
        float green = (color >> 8 & 255) / 255.0f;
        float blue = (color & 255) / 255.0f;
        
        drawFilledBox(matrices, box, red, green, blue, alpha);
    }
    
    public static void drawFilledBox(MatrixStack matrices, Box box, float red, float green, float blue, float alpha) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();
        
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        buffer.begin(VertexFormat.DrawMode.TRIANGLES, VertexFormats.POSITION_COLOR);
        
        // Draw filled box
        drawBoxFaces(buffer, matrix, box, red, green, blue, alpha);
        
        tessellator.draw();
    }
    
    private static void drawBoxOutline(BufferBuilder buffer, Matrix4f matrix, Box box, float red, float green, float blue, float alpha) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;
        
        // Bottom face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        
        // Top face
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        
        // Vertical lines
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
    }
    
    private static void drawBoxFaces(BufferBuilder buffer, Matrix4f matrix, Box box, float red, float green, float blue, float alpha) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;
        
        // Bottom face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        
        // Top face
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        
        // North face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        
        // South face
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        
        // West face
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).next();
        
        // East face
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).next();
    }
    
    public static void drawEntityBox(MatrixStack matrices, Entity entity, int color, float alpha) {
        Vec3d cameraPos = mc.gameRenderer.getCamera().getPos();
        Box box = entity.getBoundingBox().offset(-cameraPos.x, -cameraPos.y, -cameraPos.z);
        drawBox(matrices, box, color, alpha);
    }
    
    public static void drawBlockBox(MatrixStack matrices, BlockPos pos, int color, float alpha) {
        Vec3d cameraPos = mc.gameRenderer.getCamera().getPos();
        Box box = new Box(pos).offset(-cameraPos.x, -cameraPos.y, -cameraPos.z);
        drawBox(matrices, box, color, alpha);
    }
    
    public static void drawText(MatrixStack matrices, String text, float x, float y, int color) {
        TextRenderer textRenderer = mc.textRenderer;
        matrices.push();
        matrices.translate(x, y, 0);
        textRenderer.draw(matrices, Text.literal(text), 0, 0, color);
        matrices.pop();
    }
    
    public static Vec3d getEntityScreenPos(Entity entity) {
        Vec3d entityPos = entity.getPos();
        return worldToScreen(entityPos);
    }
    
    public static Vec3d worldToScreen(Vec3d worldPos) {
        Camera camera = mc.gameRenderer.getCamera();
        Vec3d cameraPos = camera.getPos();
        
        double deltaX = worldPos.x - cameraPos.x;
        double deltaY = worldPos.y - cameraPos.y;
        double deltaZ = worldPos.z - cameraPos.z;
        
        // Simple projection - this is a basic implementation
        // For more accurate projection, you'd need to use the actual projection matrix
        double distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);
        if (distance < 0.1) return null;
        
        double screenX = mc.getWindow().getScaledWidth() / 2.0 + (deltaX / distance) * 100;
        double screenY = mc.getWindow().getScaledHeight() / 2.0 - (deltaY / distance) * 100;
        
        return new Vec3d(screenX, screenY, distance);
    }
}
