package com.custommod.mixins;

import com.custommod.CustomModClient;
import net.minecraft.client.render.Camera;
import net.minecraft.entity.Entity;
import net.minecraft.world.BlockView;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(Camera.class)
public class CameraMixin {
    
    @Inject(method = "update", at = @At("HEAD"), cancellable = true)
    private void onUpdate(BlockView area, Entity focusedEntity, boolean thirdPerson, boolean inverseView, float tickDelta, CallbackInfo ci) {
        // Allow freecam to override camera behavior
        if (CustomModClient.getInstance() != null && 
            CustomModClient.getInstance().getFreecamManager().isEnabled()) {
            // Let freecam handle camera updates
            // The actual freecam logic will be handled in ClientPlayerEntityMixin
        }
    }
}
